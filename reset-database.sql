-- <PERSON><PERSON><PERSON> to reset the database for fresh start
-- Run this in your PostgreSQL database before starting the application

-- Drop all tables if they exist
DROP TABLE IF EXISTS game_actions CASCADE;
DROP TABLE IF EXISTS dialogues CASCADE;
DROP TABLE IF EXISTS backgrounds CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS game_sessions CASCADE;
DROP TABLE IF EXISTS characters CASCADE;
DROP TABLE IF EXISTS novels CASCADE;

-- Drop flyway schema history table to start fresh
DROP TABLE IF EXISTS flyway_schema_history CASCADE;

-- The application will now create all tables from scratch using the new migrations
