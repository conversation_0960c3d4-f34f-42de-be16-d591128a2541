# Create a new game session
curl -X POST http://localhost:8080/api/game/session \
  -H "Content-Type: application/json" \
  -d '{"novelId": 1, "playerName": "Player1"}'

# Get current game state
curl http://localhost:8080/api/game/session/{sessionId}

# Advance to next dialogue
curl -X POST http://localhost:8080/api/game/session/{sessionId}/next-dialogue

# Jump to chapter 2
curl -X POST http://localhost:8080/api/game/session/{sessionId}/jump-chapter/2