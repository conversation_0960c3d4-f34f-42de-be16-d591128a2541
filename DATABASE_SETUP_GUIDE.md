# Database Setup Guide

This guide will help you resolve the "missing table [backgrounds]" error and set up the database correctly.

## Problem
The error occurs because the database migrations are not in sync with the current entity structure. The original migrations were created before the Chapter entity was added, causing schema conflicts.

## Solution

### Option 1: Fresh Database Setup (Recommended)

1. **Stop the Spring Boot application** if it's running.

2. **Reset the database** by running the SQL script:
   ```bash
   # Connect to your PostgreSQL database
   psql -h localhost -p 5000 -U game -d game
   
   # Run the reset script
   \i reset-database.sql
   
   # Or manually execute:
   DROP TABLE IF EXISTS game_actions CASCADE;
   DROP TABLE IF EXISTS dialogues CASCADE;
   DROP TABLE IF EXISTS backgrounds CASCADE;
   DROP TABLE IF EXISTS chapters CASCADE;
   DROP TABLE IF EXISTS game_sessions CASCADE;
   DROP TABLE IF EXISTS characters CASCADE;
   DROP TABLE IF EXISTS novels CASCADE;
   DROP TABLE IF EXISTS flyway_schema_history CASCADE;
   ```

3. **Start the application**:
   ```bash
   ./mvnw spring-boot:run
   ```

   The application will now use the new migrations (V5 and V6) to create the complete schema with chapters.

### Option 2: Using Docker (Clean Start)

1. **Stop and remove the existing database container**:
   ```bash
   docker-compose down -v
   ```

2. **Start fresh**:
   ```bash
   docker-compose up -d
   ```

3. **Start the application**:
   ```bash
   ./mvnw spring-boot:run
   ```

### Option 3: Manual Migration Fix

If you want to keep existing data, you can manually apply the migrations:

1. **Check current migration status**:
   ```sql
   SELECT * FROM flyway_schema_history ORDER BY installed_rank;
   ```

2. **If V1-V4 were applied**, you may need to manually run V5 and V6, but this is complex due to schema conflicts.

## What's Changed

### New Migration Files
- **V5__Create_complete_schema.sql**: Complete schema with chapters from the start
- **V6__Insert_complete_sample_data.sql**: Sample data that works with the new schema

### Key Improvements
- **Proper Chapter Integration**: Chapters are now properly integrated from the beginning
- **Correct Foreign Keys**: All relationships are properly established
- **Complete Sample Data**: Rich sample data with multiple novels, chapters, and scenes

## Verification

After setup, verify the database is working:

1. **Check tables exist**:
   ```sql
   \dt
   ```
   You should see: novels, chapters, backgrounds, characters, dialogues, game_actions, game_sessions

2. **Check sample data**:
   ```sql
   SELECT n.title, c.title as chapter_title, c.chapter_number 
   FROM novels n 
   JOIN chapters c ON n.id = c.novel_id 
   ORDER BY n.id, c.chapter_number;
   ```

3. **Test the API**:
   ```bash
   curl http://localhost:8080/api/novels
   ```

## Troubleshooting

### If you still get schema validation errors:

1. **Check application.properties**:
   ```properties
   spring.jpa.hibernate.ddl-auto=none
   spring.flyway.enabled=true
   ```

2. **Clear target directory**:
   ```bash
   ./mvnw clean
   ```

3. **Check database connection**:
   ```bash
   psql -h localhost -p 5000 -U game -d game -c "SELECT version();"
   ```

### If Flyway complains about out-of-order migrations:

Add to application.properties:
```properties
spring.flyway.out-of-order=true
```

## Next Steps

Once the database is set up correctly:

1. **Access Swagger UI**: http://localhost:8080/swagger-ui.html
2. **Test game session creation**: Use the `/api/game/session` endpoint
3. **Browse novels**: Use the `/api/novels` endpoint
4. **Explore chapters**: Use the `/api/chapters/novel/{novelId}` endpoint

The database now includes:
- 3 sample novels with rich chapter structure
- 8 characters with different types and personalities
- Multiple backgrounds with environmental settings
- Sample dialogues with emotions and sequencing
- Game actions for interactive behaviors
