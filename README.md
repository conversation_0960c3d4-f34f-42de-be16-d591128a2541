# 🎮 PPT-Style Visual Novel Game Backend

A comprehensive Spring Boot backend for PPT-style visual novel games with sequential behaviors, chapter-based navigation, and rich multimedia support.

## 📋 Table of Contents

- [Features](#-features)
- [Architecture](#-architecture)
- [Quick Start](#-quick-start)
- [API Documentation](#-api-documentation)
- [Database Schema](#-database-schema)
- [Game Mechanics](#-game-mechanics)
- [Development](#-development)
- [Contributing](#-contributing)

## ✨ Features

### 🎯 Core Game Features
- **Chapter-Based Structure**: Hierarchical organization (Novel → Chapter → Background → Dialogue/Actions)
- **Sequential Navigation**: Smooth progression through dialogues, scenes, and chapters
- **Session Management**: Create, pause, resume, and save game sessions
- **Progress Tracking**: Detailed progress information and completion statistics
- **Chapter Unlocking**: Progressive chapter unlock system with conditions

### 🎵 PPT-Style Behaviors
- **Music Control**: Background music and ambient sound management
- **Visual Effects**: Fade in/out, screen shake, weather changes, time transitions
- **Character Management**: Show/hide characters with positioning and emotions
- **Timed Actions**: Auto-advance dialogues with customizable duration
- **Interactive Elements**: Click-based and auto-triggered game actions

### 🎨 Rich Content Support
- **Character System**: Multiple character types (Protagonist, NPC, Narrator)
- **Dialogue Types**: Speech, thoughts, narration, system messages
- **Emotional States**: Character emotions affecting dialogue presentation
- **Weather & Time**: Dynamic environmental effects and time-of-day changes
- **Difficulty Levels**: Chapter-based difficulty progression

### 📊 Management Features
- **Novel Library**: Browse, search, and filter visual novels
- **Statistics**: Detailed analytics for novels, chapters, and player progress
- **Session Tracking**: Multi-session support with save/load functionality
- **Content Management**: RESTful APIs for all game content

## 🏗️ Architecture

### Database Entities

```
Novel (1) ──→ (N) Chapter (1) ──→ (N) Background (1) ──→ (N) Dialogue
                                                    └──→ (N) GameAction
GameSession ──→ Novel
            ├──→ Chapter (current)
            └──→ Background (current)

Character (1) ──→ (N) Dialogue
```

### Core Entities
- **Novel**: Story metadata, author, completion status
- **Chapter**: Chapter information, unlock conditions, difficulty
- **Background**: Scene settings, images, music, environmental effects
- **Character**: Character profiles, avatars, personality traits
- **Dialogue**: Character speech with sequencing and emotions
- **GameAction**: Interactive behaviors (music, animations, effects)
- **GameSession**: Player progress and session state

### Technology Stack
- **Backend**: Spring Boot 3.3.4, JPA/Hibernate
- **Database**: PostgreSQL with Flyway migrations
- **Documentation**: OpenAPI 3.0 (Swagger)
- **Build Tool**: Maven
- **Code Quality**: Lombok, validation annotations
- **Error Handling**: Custom exceptions with proper HTTP responses

## 🚀 Quick Start

### Prerequisites
- Java 17+
- PostgreSQL 12+
- Maven 3.6+

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ppt-game
   ```

2. **Setup PostgreSQL Database**
   ```bash
   # Using Docker (recommended)
   docker-compose up -d

   # Or create database manually
   createdb -U postgres game
   ```

3. **Configure Application**
   ```properties
   # src/main/resources/application.properties
   spring.datasource.url=*************************************
   spring.datasource.username=game
   spring.datasource.password=game
   ```

4. **Run the Application**
   ```bash
   ./mvnw spring-boot:run
   ```

5. **Access the Application**
   - API Documentation: http://localhost:8080/swagger-ui.html
   - Health Check: http://localhost:8080/actuator/health

### Sample Data
The application includes sample novels with chapters, characters, and dialogues:
- "The Mysterious Academy" - Fantasy adventure (5 chapters)
- "Love in the Digital Age" - Romance story (3 chapters)
- "The Last Guardian" - Epic fantasy (8 chapters)

## 📚 API Documentation

### Game Session Management

#### Create Game Session
```bash
POST /api/game/session
Content-Type: application/json

{
  "novelId": 1,
  "playerName": "Player1",
  "startChapter": 1,
  "startScene": 1
}
```

#### Get Game State
```bash
GET /api/game/session/{sessionId}
```

#### Navigation Controls
```bash
POST /api/game/session/{sessionId}/next-dialogue     # Next dialogue
POST /api/game/session/{sessionId}/previous-dialogue # Previous dialogue
POST /api/game/session/{sessionId}/next-scene        # Next scene
POST /api/game/session/{sessionId}/previous-scene    # Previous scene
POST /api/game/session/{sessionId}/jump-chapter/{n}  # Jump to chapter
POST /api/game/session/{sessionId}/jump-scene/{n}    # Jump to scene
```

#### Session Management
```bash
POST /api/game/session/{sessionId}/save    # Save game
POST /api/game/session/{sessionId}/pause   # Pause game
POST /api/game/session/{sessionId}/resume  # Resume game
```

### Novel Management

#### Browse Novels
```bash
GET /api/novels                           # All novels
GET /api/novels/{id}                      # Novel details
GET /api/novels/{id}/with-chapters        # Novel with chapters
GET /api/novels/search/title?title=...    # Search by title
GET /api/novels/search/author?author=...  # Search by author
GET /api/novels/completed                 # Completed novels
GET /api/novels/ongoing                   # Ongoing novels
```

#### Novel Statistics
```bash
GET /api/novels/{id}/statistics           # Novel statistics
```

### Chapter Management

#### Chapter Operations
```bash
GET /api/chapters/novel/{novelId}                    # All chapters
GET /api/chapters/{id}                               # Chapter details
GET /api/chapters/{id}/with-backgrounds              # Chapter with scenes
GET /api/chapters/novel/{novelId}/number/{number}    # Specific chapter
GET /api/chapters/novel/{novelId}/completed          # Completed chapters
GET /api/chapters/novel/{novelId}/unlocked           # Unlocked chapters
```

#### Chapter Progress
```bash
GET /api/chapters/{id}/statistics                    # Chapter statistics
GET /api/chapters/novel/{novelId}/progress           # Chapter progress
```

### Response Format
All APIs return responses in this format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🗄️ Database Schema

### Core Tables
- **novels**: Story metadata and author information
- **chapters**: Chapter details with unlock conditions and difficulty
- **backgrounds**: Scene settings with multimedia references
- **characters**: Character profiles and personality data
- **dialogues**: Character speech with sequencing and emotions
- **game_actions**: Interactive behaviors and effects
- **game_sessions**: Player progress and session state

### Key Relationships
```sql
novels (1) ──→ (N) chapters (1) ──→ (N) backgrounds
characters (1) ──→ (N) dialogues (N) ←── (1) backgrounds
backgrounds (1) ──→ (N) game_actions
game_sessions ──→ novels, chapters, backgrounds
```

### Migration System
- **Flyway** for version-controlled database migrations
- **Automatic schema creation** on application startup
- **Sample data** included for immediate testing
- **Proper indexing** for optimal performance

## 🎮 Game Mechanics

### Game Flow
```mermaid
graph TD
    A[Start Game] --> B[Create Session]
    B --> C[Load Chapter 1, Scene 1]
    C --> D[Display Background]
    D --> E[Execute Scene Actions]
    E --> F[Show Dialogues]
    F --> G{User Click}
    G -->|Next Dialogue| H[Advance Dialogue]
    G -->|Next Scene| I[Advance Scene]
    G -->|Jump| J[Jump to Chapter/Scene]
    H --> K{More Dialogues?}
    K -->|Yes| F
    K -->|No| I
    I --> L{More Scenes?}
    L -->|Yes| C
    L -->|No| M[Chapter Complete]
    J --> C
    M --> N{More Chapters?}
    N -->|Yes| O[Next Chapter]
    N -->|No| P[Game Complete]
    O --> C
```

### Chapter Progression
- **Linear Progression**: Chapters unlock sequentially
- **Unlock Conditions**: Custom conditions for chapter access
- **Difficulty Scaling**: Progressive difficulty levels
- **Progress Tracking**: Completion percentages and statistics

### Action System
Game actions support various behaviors:
- **PLAY_MUSIC**: Background music control
- **CHANGE_BACKGROUND_IMAGE**: Dynamic scene changes
- **SHOW_CHARACTER**: Character appearance with positioning
- **PLAY_SOUND_EFFECT**: Audio effects and ambience
- **FADE_IN/FADE_OUT**: Visual transitions
- **SCREEN_SHAKE**: Dynamic effects
- **WEATHER_CHANGE**: Environmental effects

### Trigger Conditions
- **CLICK**: User-initiated actions
- **AUTO_TIMER**: Time-based triggers
- **DIALOGUE_END**: Action after dialogue completion
- **SCENE_START**: Actions on scene initialization
- **CUSTOM_EVENT**: Programmable triggers

## 🛠️ Development

### Project Structure
```
src/
├── main/
│   ├── java/xyz/sky/pptgame/
│   │   ├── controller/          # REST API controllers
│   │   ├── service/             # Business logic services
│   │   ├── repository/          # Data access layer
│   │   ├── entity/              # JPA entities
│   │   ├── dto/                 # Data transfer objects
│   │   └── exception/           # Custom exceptions
│   └── resources/
│       ├── db/migration/        # Flyway migrations
│       └── application.properties
└── test/                        # Unit and integration tests
```

### Building and Testing
```bash
# Build the project
./mvnw clean compile

# Run tests
./mvnw test

# Package application
./mvnw package

# Run with specific profile
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### Configuration Profiles
- **default**: Development configuration
- **test**: Testing configuration with H2 database
- **prod**: Production configuration

### Adding New Content

#### Creating a Novel
```java
Novel novel = new Novel();
novel.setTitle("My Novel");
novel.setAuthor("Author Name");
novel.setTotalChapters(5);
novelRepository.save(novel);
```

#### Adding Chapters
```java
Chapter chapter = new Chapter();
chapter.setTitle("Chapter 1");
chapter.setChapterNumber(1);
chapter.setNovel(novel);
chapter.setIsUnlocked(true);
chapterRepository.save(chapter);
```

#### Creating Backgrounds
```java
Background background = new Background();
background.setName("Forest Path");
background.setSceneNumber(1);
background.setChapter(chapter);
background.setBackgroundImageUrl("/images/forest.jpg");
backgroundRepository.save(background);
```

### Custom Game Actions
Extend the `ActionType` enum to add new behaviors:
```java
public enum ActionType {
    // Existing actions...
    CUSTOM_ANIMATION,
    PARTICLE_EFFECT,
    CAMERA_ZOOM
}
```

## 🧪 Testing

### Sample API Calls
```bash
# Create a game session
curl -X POST http://localhost:8080/api/game/session \
  -H "Content-Type: application/json" \
  -d '{"novelId": 1, "playerName": "TestPlayer"}'

# Get game state
curl http://localhost:8080/api/game/session/{sessionId}

# Navigate through the game
curl -X POST http://localhost:8080/api/game/session/{sessionId}/next-dialogue
curl -X POST http://localhost:8080/api/game/session/{sessionId}/next-scene
curl -X POST http://localhost:8080/api/game/session/{sessionId}/jump-chapter/2

# Get novel information
curl http://localhost:8080/api/novels
curl http://localhost:8080/api/novels/1/with-chapters
curl http://localhost:8080/api/chapters/novel/1
```

### Integration Testing
The application includes comprehensive integration tests covering:
- Game session lifecycle
- Navigation between chapters and scenes
- Progress tracking accuracy
- API response validation

## 🤝 Contributing

### Development Guidelines
1. **Code Style**: Follow Spring Boot conventions
2. **Testing**: Write tests for new features
3. **Documentation**: Update API documentation
4. **Database**: Use Flyway for schema changes
5. **Error Handling**: Use custom exceptions with proper HTTP codes

### Submitting Changes
1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

### Database Migrations
When adding new features requiring database changes:
```sql
-- Create new migration file: V{version}__Description.sql
-- Example: V5__Add_user_preferences.sql

CREATE TABLE user_preferences (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    preference_key VARCHAR(255) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Spring Boot team for the excellent framework
- PostgreSQL community for the robust database
- OpenAPI specification for API documentation standards

---

**Ready to create your visual novel game? Start with the Quick Start guide and explore the rich API ecosystem!** 🎮✨
