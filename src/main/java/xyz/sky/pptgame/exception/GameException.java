package xyz.sky.pptgame.exception;

public class GameException extends RuntimeException {
    
    private final String errorCode;
    
    public GameException(String message) {
        super(message);
        this.errorCode = "GAME_ERROR";
    }
    
    public GameException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public GameException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GAME_ERROR";
    }
    
    public GameException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
