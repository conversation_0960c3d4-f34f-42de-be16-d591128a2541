package xyz.sky.pptgame.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.sky.pptgame.dto.NovelSummaryDto;
import xyz.sky.pptgame.entity.Novel;
import xyz.sky.pptgame.entity.GameSession;
import xyz.sky.pptgame.exception.GameException;
import xyz.sky.pptgame.repository.NovelRepository;
import xyz.sky.pptgame.repository.BackgroundRepository;
import xyz.sky.pptgame.repository.GameSessionRepository;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class NovelService {
    
    private final NovelRepository novelRepository;
    private final BackgroundRepository backgroundRepository;
    private final GameSessionRepository gameSessionRepository;
    
    /**
     * Get all novels with summary information
     */
    public List<NovelSummaryDto> getAllNovels() {
        log.info("Fetching all novels");
        
        List<Novel> novels = novelRepository.findAllByOrderByCreatedAtDesc();
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get novel by ID
     */
    public Novel getNovelById(Long id) {
        log.info("Fetching novel with ID: {}", id);
        
        return novelRepository.findById(id)
                .orElseThrow(() -> new GameException("Novel not found with ID: " + id, "NOVEL_NOT_FOUND"));
    }
    
    /**
     * Get novel with all chapters
     */
    public Novel getNovelWithChapters(Long id) {
        log.info("Fetching novel with chapters for ID: {}", id);

        return novelRepository.findByIdWithChapters(id)
                .orElseThrow(() -> new GameException("Novel not found with ID: " + id, "NOVEL_NOT_FOUND"));
    }

    /**
     * Get novel with chapters and backgrounds
     */
    public Novel getNovelWithChaptersAndBackgrounds(Long id) {
        log.info("Fetching novel with chapters and backgrounds for ID: {}", id);

        return novelRepository.findByIdWithChaptersAndBackgrounds(id)
                .orElseThrow(() -> new GameException("Novel not found with ID: " + id, "NOVEL_NOT_FOUND"));
    }
    
    /**
     * Search novels by title
     */
    public List<NovelSummaryDto> searchNovelsByTitle(String title) {
        log.info("Searching novels by title: {}", title);
        
        List<Novel> novels = novelRepository.findByTitleContainingIgnoreCase(title);
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Search novels by author
     */
    public List<NovelSummaryDto> searchNovelsByAuthor(String author) {
        log.info("Searching novels by author: {}", author);
        
        List<Novel> novels = novelRepository.findByAuthorContainingIgnoreCase(author);
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get completed novels
     */
    public List<NovelSummaryDto> getCompletedNovels() {
        log.info("Fetching completed novels");
        
        List<Novel> novels = novelRepository.findByIsCompletedTrue();
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get ongoing novels
     */
    public List<NovelSummaryDto> getOngoingNovels() {
        log.info("Fetching ongoing novels");
        
        List<Novel> novels = novelRepository.findByIsCompletedFalse();
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get novels by chapter count range
     */
    public List<NovelSummaryDto> getNovelsByChapterRange(Integer minChapters, Integer maxChapters) {
        log.info("Fetching novels with chapters between {} and {}", minChapters, maxChapters);
        
        List<Novel> novels = novelRepository.findByTotalChaptersBetween(minChapters, maxChapters);
        
        return novels.stream()
                .map(this::mapToNovelSummaryDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get novel statistics
     */
    public NovelStatistics getNovelStatistics(Long novelId) {
        log.info("Fetching statistics for novel ID: {}", novelId);
        
        Novel novel = getNovelById(novelId);
        
        Long totalScenes = backgroundRepository.countByNovelId(novelId);
        Long activeSessions = gameSessionRepository.countByNovelIdAndSessionState(
                novelId, GameSession.SessionState.ACTIVE);
        Long completedSessions = gameSessionRepository.countByNovelIdAndSessionState(
                novelId, GameSession.SessionState.COMPLETED);
        
        return NovelStatistics.builder()
                .novelId(novelId)
                .novelTitle(novel.getTitle())
                .totalChapters(novel.getTotalChapters())
                .totalScenes(totalScenes)
                .activeSessions(activeSessions)
                .completedSessions(completedSessions)
                .isCompleted(novel.getIsCompleted())
                .build();
    }
    
    private NovelSummaryDto mapToNovelSummaryDto(Novel novel) {
        Long totalScenes = backgroundRepository.countByNovelId(novel.getId());
        Long activeSessions = gameSessionRepository.countByNovelIdAndSessionState(
                novel.getId(), GameSession.SessionState.ACTIVE);
        
        return NovelSummaryDto.builder()
                .id(novel.getId())
                .title(novel.getTitle())
                .description(novel.getDescription())
                .author(novel.getAuthor())
                .coverImageUrl(novel.getCoverImageUrl())
                .totalChapters(novel.getTotalChapters())
                .isCompleted(novel.getIsCompleted())
                .createdAt(novel.getCreatedAt())
                .updatedAt(novel.getUpdatedAt())
                .totalScenes(totalScenes)
                .activeSessions(activeSessions)
                .build();
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class NovelStatistics {
        private Long novelId;
        private String novelTitle;
        private Integer totalChapters;
        private Long totalScenes;
        private Long activeSessions;
        private Long completedSessions;
        private Boolean isCompleted;
    }
}
