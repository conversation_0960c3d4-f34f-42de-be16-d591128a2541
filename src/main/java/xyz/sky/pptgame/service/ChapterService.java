package xyz.sky.pptgame.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.sky.pptgame.entity.Chapter;
import xyz.sky.pptgame.entity.Background;
import xyz.sky.pptgame.exception.GameException;
import xyz.sky.pptgame.repository.ChapterRepository;
import xyz.sky.pptgame.repository.BackgroundRepository;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ChapterService {
    
    private final ChapterRepository chapterRepository;
    private final BackgroundRepository backgroundRepository;
    
    /**
     * Get all chapters for a novel
     */
    public List<Chapter> getChaptersByNovelId(Long novelId) {
        log.info("Fetching chapters for novel ID: {}", novelId);
        
        return chapterRepository.findByNovelIdOrderByChapterNumberAsc(novelId);
    }
    
    /**
     * Get chapter by ID
     */
    public Chapter getChapterById(Long id) {
        log.info("Fetching chapter with ID: {}", id);
        
        return chapterRepository.findById(id)
                .orElseThrow(() -> new GameException("Chapter not found with ID: " + id, "CHAPTER_NOT_FOUND"));
    }
    
    /**
     * Get chapter with backgrounds
     */
    public Chapter getChapterWithBackgrounds(Long id) {
        log.info("Fetching chapter with backgrounds for ID: {}", id);
        
        return chapterRepository.findByIdWithBackgrounds(id)
                .orElseThrow(() -> new GameException("Chapter not found with ID: " + id, "CHAPTER_NOT_FOUND"));
    }
    
    /**
     * Get chapter by novel ID and chapter number
     */
    public Chapter getChapterByNovelIdAndNumber(Long novelId, Integer chapterNumber) {
        log.info("Fetching chapter {} for novel ID: {}", chapterNumber, novelId);
        
        return chapterRepository.findByNovelIdAndChapterNumber(novelId, chapterNumber)
                .orElseThrow(() -> new GameException(
                        String.format("Chapter %d not found for novel ID: %d", chapterNumber, novelId), 
                        "CHAPTER_NOT_FOUND"));
    }
    
    /**
     * Get completed chapters for a novel
     */
    public List<Chapter> getCompletedChapters(Long novelId) {
        log.info("Fetching completed chapters for novel ID: {}", novelId);
        
        return chapterRepository.findByNovelIdAndIsCompletedTrueOrderByChapterNumberAsc(novelId);
    }
    
    /**
     * Get unlocked chapters for a novel
     */
    public List<Chapter> getUnlockedChapters(Long novelId) {
        log.info("Fetching unlocked chapters for novel ID: {}", novelId);
        
        return chapterRepository.findByNovelIdAndIsUnlockedTrueOrderByChapterNumberAsc(novelId);
    }
    
    /**
     * Get chapters by difficulty level
     */
    public List<Chapter> getChaptersByDifficulty(Long novelId, Chapter.DifficultyLevel difficultyLevel) {
        log.info("Fetching chapters with difficulty {} for novel ID: {}", difficultyLevel, novelId);
        
        return chapterRepository.findByNovelIdAndDifficultyLevelOrderByChapterNumberAsc(novelId, difficultyLevel);
    }
    
    /**
     * Get next chapter
     */
    public Chapter getNextChapter(Long novelId, Integer currentChapterNumber) {
        log.info("Fetching next chapter after {} for novel ID: {}", currentChapterNumber, novelId);
        
        return chapterRepository.findNextChapter(novelId, currentChapterNumber)
                .orElseThrow(() -> new GameException("No next chapter available", "CHAPTER_NOT_FOUND"));
    }
    
    /**
     * Get previous chapter
     */
    public Chapter getPreviousChapter(Long novelId, Integer currentChapterNumber) {
        log.info("Fetching previous chapter before {} for novel ID: {}", currentChapterNumber, novelId);
        
        return chapterRepository.findPreviousChapter(novelId, currentChapterNumber)
                .orElseThrow(() -> new GameException("No previous chapter available", "CHAPTER_NOT_FOUND"));
    }
    
    /**
     * Get chapter statistics
     */
    public ChapterStatistics getChapterStatistics(Long chapterId) {
        log.info("Fetching statistics for chapter ID: {}", chapterId);
        
        Chapter chapter = getChapterById(chapterId);
        Long totalScenes = backgroundRepository.countByChapterId(chapterId);
        
        return ChapterStatistics.builder()
                .chapterId(chapterId)
                .chapterNumber(chapter.getChapterNumber())
                .chapterTitle(chapter.getTitle())
                .totalScenes(totalScenes)
                .isCompleted(chapter.getIsCompleted())
                .isUnlocked(chapter.getIsUnlocked())
                .difficultyLevel(chapter.getDifficultyLevel())
                .estimatedDurationMinutes(chapter.getEstimatedDurationMinutes())
                .build();
    }
    
    /**
     * Check if chapter is unlocked
     */
    public boolean isChapterUnlocked(Long novelId, Integer chapterNumber) {
        return chapterRepository.findByNovelIdAndChapterNumber(novelId, chapterNumber)
                .map(Chapter::getIsUnlocked)
                .orElse(false);
    }
    
    /**
     * Get chapter progress for a novel
     */
    public ChapterProgress getChapterProgress(Long novelId) {
        log.info("Fetching chapter progress for novel ID: {}", novelId);
        
        Long totalChapters = chapterRepository.countByNovelId(novelId);
        Long completedChapters = chapterRepository.countByNovelIdAndIsCompletedTrue(novelId);
        Long unlockedChapters = chapterRepository.countByNovelIdAndIsUnlockedTrue(novelId);
        
        double completionPercentage = totalChapters > 0 ? 
                (double) completedChapters / totalChapters * 100 : 0;
        
        return ChapterProgress.builder()
                .novelId(novelId)
                .totalChapters(totalChapters)
                .completedChapters(completedChapters)
                .unlockedChapters(unlockedChapters)
                .completionPercentage(completionPercentage)
                .build();
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ChapterStatistics {
        private Long chapterId;
        private Integer chapterNumber;
        private String chapterTitle;
        private Long totalScenes;
        private Boolean isCompleted;
        private Boolean isUnlocked;
        private Chapter.DifficultyLevel difficultyLevel;
        private Integer estimatedDurationMinutes;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ChapterProgress {
        private Long novelId;
        private Long totalChapters;
        private Long completedChapters;
        private Long unlockedChapters;
        private Double completionPercentage;
    }
}
