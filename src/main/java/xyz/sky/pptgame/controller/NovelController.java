package xyz.sky.pptgame.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import xyz.sky.pptgame.dto.ApiResponse;
import xyz.sky.pptgame.dto.NovelSummaryDto;
import xyz.sky.pptgame.entity.Novel;
import xyz.sky.pptgame.exception.GameException;
import xyz.sky.pptgame.service.NovelService;

import java.util.List;

@RestController
@RequestMapping("/api/novels")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Novel Controller", description = "APIs for novel management and browsing")
public class NovelController {
    
    private final NovelService novelService;
    
    @Operation(summary = "Get all novels", description = "Retrieves a list of all available novels with summary information")
    @GetMapping
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> getAllNovels() {
        try {
            log.info("Fetching all novels");
            
            List<NovelSummaryDto> novels = novelService.getAllNovels();
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Novels retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching novels", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch novels"));
        }
    }
    
    @Operation(summary = "Get novel by ID", description = "Retrieves detailed information about a specific novel")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Novel>> getNovelById(
            @Parameter(description = "Novel ID") @PathVariable Long id) {
        try {
            log.info("Fetching novel with ID: {}", id);
            
            Novel novel = novelService.getNovelById(id);
            
            return ResponseEntity.ok(ApiResponse.success(novel, "Novel retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching novel: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching novel", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch novel"));
        }
    }
    
    @Operation(summary = "Get novel with backgrounds", description = "Retrieves a novel with all its background scenes")
    @GetMapping("/{id}/with-backgrounds")
    public ResponseEntity<ApiResponse<Novel>> getNovelWithBackgrounds(
            @Parameter(description = "Novel ID") @PathVariable Long id) {
        try {
            log.info("Fetching novel with backgrounds for ID: {}", id);
            
            Novel novel = novelService.getNovelWithBackgrounds(id);
            
            return ResponseEntity.ok(ApiResponse.success(novel, "Novel with backgrounds retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching novel with backgrounds: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching novel with backgrounds", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch novel with backgrounds"));
        }
    }
    
    @Operation(summary = "Search novels by title", description = "Searches for novels by title (case-insensitive)")
    @GetMapping("/search/title")
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> searchNovelsByTitle(
            @Parameter(description = "Title to search for") @RequestParam String title) {
        try {
            log.info("Searching novels by title: {}", title);
            
            List<NovelSummaryDto> novels = novelService.searchNovelsByTitle(title);
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Search completed successfully"));
        } catch (Exception e) {
            log.error("Error searching novels by title", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to search novels"));
        }
    }
    
    @Operation(summary = "Search novels by author", description = "Searches for novels by author (case-insensitive)")
    @GetMapping("/search/author")
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> searchNovelsByAuthor(
            @Parameter(description = "Author to search for") @RequestParam String author) {
        try {
            log.info("Searching novels by author: {}", author);
            
            List<NovelSummaryDto> novels = novelService.searchNovelsByAuthor(author);
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Search completed successfully"));
        } catch (Exception e) {
            log.error("Error searching novels by author", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to search novels"));
        }
    }
    
    @Operation(summary = "Get completed novels", description = "Retrieves all completed novels")
    @GetMapping("/completed")
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> getCompletedNovels() {
        try {
            log.info("Fetching completed novels");
            
            List<NovelSummaryDto> novels = novelService.getCompletedNovels();
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Completed novels retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching completed novels", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch completed novels"));
        }
    }
    
    @Operation(summary = "Get ongoing novels", description = "Retrieves all ongoing (incomplete) novels")
    @GetMapping("/ongoing")
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> getOngoingNovels() {
        try {
            log.info("Fetching ongoing novels");
            
            List<NovelSummaryDto> novels = novelService.getOngoingNovels();
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Ongoing novels retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching ongoing novels", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch ongoing novels"));
        }
    }
    
    @Operation(summary = "Get novels by chapter range", description = "Retrieves novels within a specific chapter count range")
    @GetMapping("/by-chapters")
    public ResponseEntity<ApiResponse<List<NovelSummaryDto>>> getNovelsByChapterRange(
            @Parameter(description = "Minimum number of chapters") @RequestParam Integer minChapters,
            @Parameter(description = "Maximum number of chapters") @RequestParam Integer maxChapters) {
        try {
            log.info("Fetching novels with chapters between {} and {}", minChapters, maxChapters);
            
            List<NovelSummaryDto> novels = novelService.getNovelsByChapterRange(minChapters, maxChapters);
            
            return ResponseEntity.ok(ApiResponse.success(novels, "Novels retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching novels by chapter range", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch novels"));
        }
    }
    
    @Operation(summary = "Get novel statistics", description = "Retrieves detailed statistics for a specific novel")
    @GetMapping("/{id}/statistics")
    public ResponseEntity<ApiResponse<NovelService.NovelStatistics>> getNovelStatistics(
            @Parameter(description = "Novel ID") @PathVariable Long id) {
        try {
            log.info("Fetching statistics for novel ID: {}", id);
            
            NovelService.NovelStatistics statistics = novelService.getNovelStatistics(id);
            
            return ResponseEntity.ok(ApiResponse.success(statistics, "Statistics retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching novel statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching novel statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch novel statistics"));
        }
    }
}
