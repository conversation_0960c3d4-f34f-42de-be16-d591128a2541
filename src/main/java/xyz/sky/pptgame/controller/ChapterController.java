package xyz.sky.pptgame.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import xyz.sky.pptgame.dto.ApiResponse;
import xyz.sky.pptgame.entity.Chapter;
import xyz.sky.pptgame.exception.GameException;
import xyz.sky.pptgame.service.ChapterService;

import java.util.List;

@RestController
@RequestMapping("/api/chapters")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Chapter Controller", description = "APIs for chapter management and navigation")
public class ChapterController {
    
    private final ChapterService chapterService;
    
    @Operation(summary = "Get chapters by novel ID", description = "Retrieves all chapters for a specific novel")
    @GetMapping("/novel/{novelId}")
    public ResponseEntity<ApiResponse<List<Chapter>>> getChaptersByNovelId(
            @Parameter(description = "Novel ID") @PathVariable Long novelId) {
        try {
            log.info("Fetching chapters for novel ID: {}", novelId);
            
            List<Chapter> chapters = chapterService.getChaptersByNovelId(novelId);
            
            return ResponseEntity.ok(ApiResponse.success(chapters, "Chapters retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching chapters for novel", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapters"));
        }
    }
    
    @Operation(summary = "Get chapter by ID", description = "Retrieves detailed information about a specific chapter")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Chapter>> getChapterById(
            @Parameter(description = "Chapter ID") @PathVariable Long id) {
        try {
            log.info("Fetching chapter with ID: {}", id);
            
            Chapter chapter = chapterService.getChapterById(id);
            
            return ResponseEntity.ok(ApiResponse.success(chapter, "Chapter retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching chapter: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching chapter", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapter"));
        }
    }
    
    @Operation(summary = "Get chapter with backgrounds", description = "Retrieves a chapter with all its background scenes")
    @GetMapping("/{id}/with-backgrounds")
    public ResponseEntity<ApiResponse<Chapter>> getChapterWithBackgrounds(
            @Parameter(description = "Chapter ID") @PathVariable Long id) {
        try {
            log.info("Fetching chapter with backgrounds for ID: {}", id);
            
            Chapter chapter = chapterService.getChapterWithBackgrounds(id);
            
            return ResponseEntity.ok(ApiResponse.success(chapter, "Chapter with backgrounds retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching chapter with backgrounds: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching chapter with backgrounds", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapter with backgrounds"));
        }
    }
    
    @Operation(summary = "Get chapter by novel and number", description = "Retrieves a chapter by novel ID and chapter number")
    @GetMapping("/novel/{novelId}/number/{chapterNumber}")
    public ResponseEntity<ApiResponse<Chapter>> getChapterByNovelIdAndNumber(
            @Parameter(description = "Novel ID") @PathVariable Long novelId,
            @Parameter(description = "Chapter number") @PathVariable Integer chapterNumber) {
        try {
            log.info("Fetching chapter {} for novel ID: {}", chapterNumber, novelId);
            
            Chapter chapter = chapterService.getChapterByNovelIdAndNumber(novelId, chapterNumber);
            
            return ResponseEntity.ok(ApiResponse.success(chapter, "Chapter retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching chapter: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching chapter", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapter"));
        }
    }
    
    @Operation(summary = "Get completed chapters", description = "Retrieves all completed chapters for a novel")
    @GetMapping("/novel/{novelId}/completed")
    public ResponseEntity<ApiResponse<List<Chapter>>> getCompletedChapters(
            @Parameter(description = "Novel ID") @PathVariable Long novelId) {
        try {
            log.info("Fetching completed chapters for novel ID: {}", novelId);
            
            List<Chapter> chapters = chapterService.getCompletedChapters(novelId);
            
            return ResponseEntity.ok(ApiResponse.success(chapters, "Completed chapters retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching completed chapters", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch completed chapters"));
        }
    }
    
    @Operation(summary = "Get unlocked chapters", description = "Retrieves all unlocked chapters for a novel")
    @GetMapping("/novel/{novelId}/unlocked")
    public ResponseEntity<ApiResponse<List<Chapter>>> getUnlockedChapters(
            @Parameter(description = "Novel ID") @PathVariable Long novelId) {
        try {
            log.info("Fetching unlocked chapters for novel ID: {}", novelId);
            
            List<Chapter> chapters = chapterService.getUnlockedChapters(novelId);
            
            return ResponseEntity.ok(ApiResponse.success(chapters, "Unlocked chapters retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching unlocked chapters", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch unlocked chapters"));
        }
    }
    
    @Operation(summary = "Get chapters by difficulty", description = "Retrieves chapters filtered by difficulty level")
    @GetMapping("/novel/{novelId}/difficulty/{difficultyLevel}")
    public ResponseEntity<ApiResponse<List<Chapter>>> getChaptersByDifficulty(
            @Parameter(description = "Novel ID") @PathVariable Long novelId,
            @Parameter(description = "Difficulty level") @PathVariable Chapter.DifficultyLevel difficultyLevel) {
        try {
            log.info("Fetching chapters with difficulty {} for novel ID: {}", difficultyLevel, novelId);
            
            List<Chapter> chapters = chapterService.getChaptersByDifficulty(novelId, difficultyLevel);
            
            return ResponseEntity.ok(ApiResponse.success(chapters, "Chapters retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching chapters by difficulty", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapters"));
        }
    }
    
    @Operation(summary = "Get chapter statistics", description = "Retrieves detailed statistics for a specific chapter")
    @GetMapping("/{id}/statistics")
    public ResponseEntity<ApiResponse<ChapterService.ChapterStatistics>> getChapterStatistics(
            @Parameter(description = "Chapter ID") @PathVariable Long id) {
        try {
            log.info("Fetching statistics for chapter ID: {}", id);
            
            ChapterService.ChapterStatistics statistics = chapterService.getChapterStatistics(id);
            
            return ResponseEntity.ok(ApiResponse.success(statistics, "Statistics retrieved successfully"));
        } catch (GameException e) {
            log.error("Game error fetching chapter statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error fetching chapter statistics", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapter statistics"));
        }
    }
    
    @Operation(summary = "Get chapter progress", description = "Retrieves chapter progress information for a novel")
    @GetMapping("/novel/{novelId}/progress")
    public ResponseEntity<ApiResponse<ChapterService.ChapterProgress>> getChapterProgress(
            @Parameter(description = "Novel ID") @PathVariable Long novelId) {
        try {
            log.info("Fetching chapter progress for novel ID: {}", novelId);
            
            ChapterService.ChapterProgress progress = chapterService.getChapterProgress(novelId);
            
            return ResponseEntity.ok(ApiResponse.success(progress, "Chapter progress retrieved successfully"));
        } catch (Exception e) {
            log.error("Error fetching chapter progress", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to fetch chapter progress"));
        }
    }
    
    @Operation(summary = "Check if chapter is unlocked", description = "Checks if a specific chapter is unlocked")
    @GetMapping("/novel/{novelId}/number/{chapterNumber}/unlocked")
    public ResponseEntity<ApiResponse<Boolean>> isChapterUnlocked(
            @Parameter(description = "Novel ID") @PathVariable Long novelId,
            @Parameter(description = "Chapter number") @PathVariable Integer chapterNumber) {
        try {
            log.info("Checking if chapter {} is unlocked for novel ID: {}", chapterNumber, novelId);
            
            boolean isUnlocked = chapterService.isChapterUnlocked(novelId, chapterNumber);
            
            return ResponseEntity.ok(ApiResponse.success(isUnlocked, "Chapter unlock status retrieved successfully"));
        } catch (Exception e) {
            log.error("Error checking chapter unlock status", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to check chapter unlock status"));
        }
    }
}
