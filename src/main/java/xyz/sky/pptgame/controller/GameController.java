package xyz.sky.pptgame.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import xyz.sky.pptgame.dto.*;
import xyz.sky.pptgame.exception.GameException;
import xyz.sky.pptgame.service.GameService;

@RestController
@RequestMapping("/api/game")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Game Controller", description = "APIs for game session management and gameplay")
public class GameController {
    
    private final GameService gameService;
    
    @Operation(summary = "Create a new game session", description = "Creates a new game session for a novel")
    @PostMapping("/session")
    public ResponseEntity<ApiResponse<GameStateResponse>> createSession(
            @Valid @RequestBody CreateSessionRequest request) {
        try {
            log.info("Creating game session for novel ID: {}", request.getNovelId());
            
            GameStateResponse gameState = gameService.createSession(request);
            
            return ResponseEntity.ok(ApiResponse.success(gameState, "Game session created successfully"));
        } catch (GameException e) {
            log.error("Game error creating session: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error creating session", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to create game session"));
        }
    }
    
    @Operation(summary = "Get current game state", description = "Retrieves the current state of a game session")
    @GetMapping("/session/{sessionId}")
    public ResponseEntity<ApiResponse<GameStateResponse>> getGameState(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        try {
            log.info("Getting game state for session: {}", sessionId);
            
            GameStateResponse gameState = gameService.getGameState(sessionId);
            
            return ResponseEntity.ok(ApiResponse.success(gameState));
        } catch (GameException e) {
            log.error("Game error getting state: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error getting game state", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to get game state"));
        }
    }
    
    @Operation(summary = "Execute game action", description = "Executes a game action like next dialogue, scene navigation, etc.")
    @PostMapping("/action")
    public ResponseEntity<ApiResponse<GameStateResponse>> executeAction(
            @Valid @RequestBody GameActionRequest request) {
        try {
            log.info("Executing action: {} for session: {}", request.getActionType(), request.getSessionId());
            
            GameStateResponse gameState = gameService.executeAction(request);
            
            return ResponseEntity.ok(ApiResponse.success(gameState, "Action executed successfully"));
        } catch (GameException e) {
            log.error("Game error executing action: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), e.getErrorCode()));
        } catch (Exception e) {
            log.error("Unexpected error executing action", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to execute action"));
        }
    }
    
    @Operation(summary = "Next dialogue", description = "Advances to the next dialogue in the current scene")
    @PostMapping("/session/{sessionId}/next-dialogue")
    public ResponseEntity<ApiResponse<GameStateResponse>> nextDialogue(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.NEXT_DIALOGUE)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Previous dialogue", description = "Goes back to the previous dialogue in the current scene")
    @PostMapping("/session/{sessionId}/previous-dialogue")
    public ResponseEntity<ApiResponse<GameStateResponse>> previousDialogue(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.PREVIOUS_DIALOGUE)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Next scene", description = "Advances to the next scene")
    @PostMapping("/session/{sessionId}/next-scene")
    public ResponseEntity<ApiResponse<GameStateResponse>> nextScene(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.NEXT_SCENE)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Previous scene", description = "Goes back to the previous scene")
    @PostMapping("/session/{sessionId}/previous-scene")
    public ResponseEntity<ApiResponse<GameStateResponse>> previousScene(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.PREVIOUS_SCENE)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Jump to chapter", description = "Jumps to a specific chapter")
    @PostMapping("/session/{sessionId}/jump-chapter/{chapterNumber}")
    public ResponseEntity<ApiResponse<GameStateResponse>> jumpToChapter(
            @Parameter(description = "Game session ID") @PathVariable String sessionId,
            @Parameter(description = "Chapter number to jump to") @PathVariable Integer chapterNumber) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.JUMP_TO_CHAPTER)
                .actionData(chapterNumber)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Jump to scene", description = "Jumps to a specific scene in the current chapter")
    @PostMapping("/session/{sessionId}/jump-scene/{sceneNumber}")
    public ResponseEntity<ApiResponse<GameStateResponse>> jumpToScene(
            @Parameter(description = "Game session ID") @PathVariable String sessionId,
            @Parameter(description = "Scene number to jump to") @PathVariable Integer sceneNumber) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.JUMP_TO_SCENE)
                .actionData(sceneNumber)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Save game", description = "Saves the current game state")
    @PostMapping("/session/{sessionId}/save")
    public ResponseEntity<ApiResponse<GameStateResponse>> saveGame(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.SAVE_GAME)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Pause game", description = "Pauses the current game session")
    @PostMapping("/session/{sessionId}/pause")
    public ResponseEntity<ApiResponse<GameStateResponse>> pauseGame(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.PAUSE_GAME)
                .build();
        
        return executeAction(request);
    }
    
    @Operation(summary = "Resume game", description = "Resumes a paused game session")
    @PostMapping("/session/{sessionId}/resume")
    public ResponseEntity<ApiResponse<GameStateResponse>> resumeGame(
            @Parameter(description = "Game session ID") @PathVariable String sessionId) {
        
        GameActionRequest request = GameActionRequest.builder()
                .sessionId(sessionId)
                .actionType(GameActionRequest.ActionType.RESUME_GAME)
                .build();
        
        return executeAction(request);
    }
}
