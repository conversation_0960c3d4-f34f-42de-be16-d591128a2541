package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Character;

import java.util.List;
import java.util.Optional;

@Repository
public interface CharacterRepository extends JpaRepository<Character, Long> {
    
    /**
     * Find characters by name
     */
    List<Character> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find characters by type
     */
    List<Character> findByCharacterType(Character.CharacterType characterType);
    
    /**
     * Find character with dialogues
     */
    @Query("SELECT c FROM Character c LEFT JOIN FETCH c.dialogues WHERE c.id = :id")
    Optional<Character> findByIdWithDialogues(@Param("id") Long id);
    
    /**
     * Find all protagonists
     */
    List<Character> findByCharacterTypeOrderByNameAsc(Character.CharacterType characterType);
    
    /**
     * Find characters ordered by name
     */
    List<Character> findAllByOrderByNameAsc();
    
    /**
     * Check if character exists by name
     */
    boolean existsByNameIgnoreCase(String name);
}
