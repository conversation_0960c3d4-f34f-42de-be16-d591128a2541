package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.GameAction;

import java.util.List;
import java.util.Optional;

@Repository
public interface GameActionRepository extends JpaRepository<GameAction, Long> {
    
    /**
     * Find actions by background ID ordered by sequence
     */
    List<GameAction> findByBackgroundIdOrderBySequenceOrderAsc(Long backgroundId);
    
    /**
     * Find actions by background and trigger condition
     */
    List<GameAction> findByBackgroundIdAndTriggerConditionOrderBySequenceOrderAsc(
            Long backgroundId, GameAction.TriggerCondition triggerCondition);
    
    /**
     * Find actions by action type
     */
    List<GameAction> findByActionTypeOrderBySequenceOrderAsc(GameAction.ActionType actionType);
    
    /**
     * Find action by background and sequence order
     */
    Optional<GameAction> findByBackgroundIdAndSequenceOrder(Long backgroundId, Integer sequenceOrder);
    
    /**
     * Find next action in sequence
     */
    @Query("SELECT a FROM GameAction a WHERE a.background.id = :backgroundId " +
           "AND a.sequenceOrder > :currentSequence " +
           "ORDER BY a.sequenceOrder ASC")
    Optional<GameAction> findNextAction(@Param("backgroundId") Long backgroundId, 
                                       @Param("currentSequence") Integer currentSequence);
    
    /**
     * Find previous action in sequence
     */
    @Query("SELECT a FROM GameAction a WHERE a.background.id = :backgroundId " +
           "AND a.sequenceOrder < :currentSequence " +
           "ORDER BY a.sequenceOrder DESC")
    Optional<GameAction> findPreviousAction(@Param("backgroundId") Long backgroundId, 
                                           @Param("currentSequence") Integer currentSequence);
    
    /**
     * Find blocking actions
     */
    List<GameAction> findByBackgroundIdAndIsBlockingTrueOrderBySequenceOrderAsc(Long backgroundId);
    
    /**
     * Get total action count for a background
     */
    Long countByBackgroundId(Long backgroundId);
    
    /**
     * Find max sequence order for a background
     */
    @Query("SELECT MAX(a.sequenceOrder) FROM GameAction a WHERE a.background.id = :backgroundId")
    Optional<Integer> findMaxSequenceOrderByBackgroundId(@Param("backgroundId") Long backgroundId);
    
    /**
     * Find actions that should auto-trigger
     */
    List<GameAction> findByBackgroundIdAndTriggerConditionInOrderBySequenceOrderAsc(
            Long backgroundId, List<GameAction.TriggerCondition> triggerConditions);
}
