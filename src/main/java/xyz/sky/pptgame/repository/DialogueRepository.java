package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Dialogue;

import java.util.List;
import java.util.Optional;

@Repository
public interface DialogueRepository extends JpaRepository<Dialogue, Long> {
    
    /**
     * Find dialogues by background ID ordered by sequence
     */
    List<Dialogue> findByBackgroundIdOrderBySequenceOrderAsc(Long backgroundId);
    
    /**
     * Find dialogues by character ID
     */
    List<Dialogue> findByCharacterIdOrderBySequenceOrderAsc(Long characterId);
    
    /**
     * Find dialogues by background and character
     */
    List<Dialogue> findByBackgroundIdAndCharacterIdOrderBySequenceOrderAsc(Long backgroundId, Long characterId);
    
    /**
     * Find dialogue by background and sequence order
     */
    Optional<Dialogue> findByBackgroundIdAndSequenceOrder(Long backgroundId, Integer sequenceOrder);
    
    /**
     * Find next dialogue in sequence
     */
    @Query("SELECT d FROM Dialogue d WHERE d.background.id = :backgroundId " +
           "AND d.sequenceOrder > :currentSequence " +
           "ORDER BY d.sequenceOrder ASC")
    Optional<Dialogue> findNextDialogue(@Param("backgroundId") Long backgroundId, 
                                       @Param("currentSequence") Integer currentSequence);
    
    /**
     * Find previous dialogue in sequence
     */
    @Query("SELECT d FROM Dialogue d WHERE d.background.id = :backgroundId " +
           "AND d.sequenceOrder < :currentSequence " +
           "ORDER BY d.sequenceOrder DESC")
    Optional<Dialogue> findPreviousDialogue(@Param("backgroundId") Long backgroundId, 
                                           @Param("currentSequence") Integer currentSequence);
    
    /**
     * Get total dialogue count for a background
     */
    Long countByBackgroundId(Long backgroundId);
    
    /**
     * Find dialogues by type
     */
    List<Dialogue> findByDialogueTypeOrderBySequenceOrderAsc(Dialogue.DialogueType dialogueType);
    
    /**
     * Find max sequence order for a background
     */
    @Query("SELECT MAX(d.sequenceOrder) FROM Dialogue d WHERE d.background.id = :backgroundId")
    Optional<Integer> findMaxSequenceOrderByBackgroundId(@Param("backgroundId") Long backgroundId);
}
