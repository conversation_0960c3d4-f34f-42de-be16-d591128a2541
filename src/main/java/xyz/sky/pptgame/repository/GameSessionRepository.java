package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.GameSession;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GameSessionRepository extends JpaRepository<GameSession, Long> {
    
    /**
     * Find session by session ID
     */
    Optional<GameSession> findBySessionId(String sessionId);
    
    /**
     * Find sessions by novel ID
     */
    List<GameSession> findByNovelIdOrderByLastAccessedAtDesc(Long novelId);
    
    /**
     * Find sessions by player name
     */
    List<GameSession> findByPlayerNameContainingIgnoreCaseOrderByLastAccessedAtDesc(String playerName);
    
    /**
     * Find active sessions
     */
    List<GameSession> findBySessionStateOrderByLastAccessedAtDesc(GameSession.SessionState sessionState);
    
    /**
     * Find session with novel and current background
     */
    @Query("SELECT s FROM GameSession s " +
           "LEFT JOIN FETCH s.novel " +
           "LEFT JOIN FETCH s.currentBackground " +
           "WHERE s.sessionId = :sessionId")
    Optional<GameSession> findBySessionIdWithDetails(@Param("sessionId") String sessionId);
    
    /**
     * Find sessions that haven't been accessed recently
     */
    List<GameSession> findByLastAccessedAtBeforeAndSessionState(
            LocalDateTime cutoffTime, GameSession.SessionState sessionState);
    
    /**
     * Find sessions by novel and state
     */
    List<GameSession> findByNovelIdAndSessionStateOrderByLastAccessedAtDesc(
            Long novelId, GameSession.SessionState sessionState);
    
    /**
     * Check if session exists by session ID
     */
    boolean existsBySessionId(String sessionId);
    
    /**
     * Count active sessions for a novel
     */
    Long countByNovelIdAndSessionState(Long novelId, GameSession.SessionState sessionState);
    
    /**
     * Find recent sessions
     */
    List<GameSession> findByLastAccessedAtAfterOrderByLastAccessedAtDesc(LocalDateTime since);
}
