package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Novel;

import java.util.List;
import java.util.Optional;

@Repository
public interface NovelRepository extends JpaRepository<Novel, Long> {
    
    /**
     * Find novels by author
     */
    List<Novel> findByAuthorContainingIgnoreCase(String author);
    
    /**
     * Find novels by title
     */
    List<Novel> findByTitleContainingIgnoreCase(String title);
    
    /**
     * Find completed novels
     */
    List<Novel> findByIsCompletedTrue();
    
    /**
     * Find ongoing novels
     */
    List<Novel> findByIsCompletedFalse();
    
    /**
     * Find novel with its chapters
     */
    @Query("SELECT n FROM Novel n LEFT JOIN FETCH n.chapters WHERE n.id = :id")
    Optional<Novel> findByIdWithChapters(@Param("id") Long id);

    /**
     * Find novel with chapters and backgrounds
     */
    @Query("SELECT n FROM Novel n LEFT JOIN FETCH n.chapters c LEFT JOIN FETCH c.backgrounds WHERE n.id = :id")
    Optional<Novel> findByIdWithChaptersAndBackgrounds(@Param("id") Long id);
    
    /**
     * Find novels ordered by creation date
     */
    List<Novel> findAllByOrderByCreatedAtDesc();
    
    /**
     * Find novels by total chapters range
     */
    List<Novel> findByTotalChaptersBetween(Integer minChapters, Integer maxChapters);
}
