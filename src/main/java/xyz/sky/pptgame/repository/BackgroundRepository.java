package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Background;

import java.util.List;
import java.util.Optional;

@Repository
public interface BackgroundRepository extends JpaRepository<Background, Long> {
    
    /**
     * Find backgrounds by novel ID
     */
    List<Background> findByNovelIdOrderByChapterNumberAscSceneNumberAsc(Long novelId);
    
    /**
     * Find backgrounds by novel ID and chapter
     */
    List<Background> findByNovelIdAndChapterNumberOrderBySceneNumberAsc(Long novelId, Integer chapterNumber);
    
    /**
     * Find specific background by novel, chapter, and scene
     */
    Optional<Background> findByNovelIdAndChapterNumberAndSceneNumber(Long novelId, Integer chapterNumber, Integer sceneNumber);
    
    /**
     * Find background with dialogues and actions
     */
    @Query("SELECT b FROM Background b " +
           "LEFT JOIN FETCH b.dialogues d " +
           "LEFT JOIN FETCH b.gameActions a " +
           "WHERE b.id = :id " +
           "ORDER BY d.sequenceOrder ASC, a.sequenceOrder ASC")
    Optional<Background> findByIdWithDialoguesAndActions(@Param("id") Long id);
    
    /**
     * Find next background in sequence
     */
    @Query("SELECT b FROM Background b WHERE b.novel.id = :novelId " +
           "AND ((b.chapterNumber = :chapterNumber AND b.sceneNumber > :sceneNumber) " +
           "OR b.chapterNumber > :chapterNumber) " +
           "ORDER BY b.chapterNumber ASC, b.sceneNumber ASC")
    Optional<Background> findNextBackground(@Param("novelId") Long novelId, 
                                          @Param("chapterNumber") Integer chapterNumber, 
                                          @Param("sceneNumber") Integer sceneNumber);
    
    /**
     * Find previous background in sequence
     */
    @Query("SELECT b FROM Background b WHERE b.novel.id = :novelId " +
           "AND ((b.chapterNumber = :chapterNumber AND b.sceneNumber < :sceneNumber) " +
           "OR b.chapterNumber < :chapterNumber) " +
           "ORDER BY b.chapterNumber DESC, b.sceneNumber DESC")
    Optional<Background> findPreviousBackground(@Param("novelId") Long novelId, 
                                              @Param("chapterNumber") Integer chapterNumber, 
                                              @Param("sceneNumber") Integer sceneNumber);
    
    /**
     * Get total scenes in a chapter
     */
    @Query("SELECT COUNT(b) FROM Background b WHERE b.novel.id = :novelId AND b.chapterNumber = :chapterNumber")
    Long countScenesInChapter(@Param("novelId") Long novelId, @Param("chapterNumber") Integer chapterNumber);
    
    /**
     * Get max chapter number for a novel
     */
    @Query("SELECT MAX(b.chapterNumber) FROM Background b WHERE b.novel.id = :novelId")
    Optional<Integer> findMaxChapterNumber(@Param("novelId") Long novelId);

    /**
     * Count total backgrounds/scenes for a novel
     */
    Long countByNovelId(Long novelId);
}
