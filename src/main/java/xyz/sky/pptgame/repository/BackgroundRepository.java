package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Background;

import java.util.List;
import java.util.Optional;

@Repository
public interface BackgroundRepository extends JpaRepository<Background, Long> {
    
    /**
     * Find backgrounds by chapter ID
     */
    List<Background> findByChapterIdOrderBySceneNumberAsc(Long chapterId);

    /**
     * Find backgrounds by novel ID (through chapter relationship)
     */
    @Query("SELECT b FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId ORDER BY c.chapterNumber ASC, b.sceneNumber ASC")
    List<Background> findByNovelIdOrderByChapterNumberAscSceneNumberAsc(@Param("novelId") Long novelId);

    /**
     * Find backgrounds by novel ID and chapter number
     */
    @Query("SELECT b FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId AND c.chapterNumber = :chapterNumber ORDER BY b.sceneNumber ASC")
    List<Background> findByNovelIdAndChapterNumberOrderBySceneNumberAsc(@Param("novelId") Long novelId, @Param("chapterNumber") Integer chapterNumber);
    
    /**
     * Find specific background by chapter ID and scene number
     */
    Optional<Background> findByChapterIdAndSceneNumber(Long chapterId, Integer sceneNumber);

    /**
     * Find specific background by novel, chapter, and scene
     */
    @Query("SELECT b FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId AND c.chapterNumber = :chapterNumber AND b.sceneNumber = :sceneNumber")
    Optional<Background> findByNovelIdAndChapterNumberAndSceneNumber(@Param("novelId") Long novelId, @Param("chapterNumber") Integer chapterNumber, @Param("sceneNumber") Integer sceneNumber);
    
    /**
     * Find background with dialogues and actions
     */
    @Query("SELECT b FROM Background b " +
           "LEFT JOIN FETCH b.dialogues d " +
           "LEFT JOIN FETCH b.gameActions a " +
           "WHERE b.id = :id " +
           "ORDER BY d.sequenceOrder ASC, a.sequenceOrder ASC")
    Optional<Background> findByIdWithDialoguesAndActions(@Param("id") Long id);
    
    /**
     * Find next background in sequence within chapter
     */
    @Query("SELECT b FROM Background b WHERE b.chapter.id = :chapterId AND b.sceneNumber > :sceneNumber ORDER BY b.sceneNumber ASC")
    Optional<Background> findNextBackgroundInChapter(@Param("chapterId") Long chapterId, @Param("sceneNumber") Integer sceneNumber);

    /**
     * Find previous background in sequence within chapter
     */
    @Query("SELECT b FROM Background b WHERE b.chapter.id = :chapterId AND b.sceneNumber < :sceneNumber ORDER BY b.sceneNumber DESC")
    Optional<Background> findPreviousBackgroundInChapter(@Param("chapterId") Long chapterId, @Param("sceneNumber") Integer sceneNumber);

    /**
     * Find next background in sequence across chapters
     */
    @Query("SELECT b FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId " +
           "AND ((c.chapterNumber = :chapterNumber AND b.sceneNumber > :sceneNumber) " +
           "OR c.chapterNumber > :chapterNumber) " +
           "ORDER BY c.chapterNumber ASC, b.sceneNumber ASC")
    Optional<Background> findNextBackground(@Param("novelId") Long novelId,
                                          @Param("chapterNumber") Integer chapterNumber,
                                          @Param("sceneNumber") Integer sceneNumber);

    /**
     * Find previous background in sequence across chapters
     */
    @Query("SELECT b FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId " +
           "AND ((c.chapterNumber = :chapterNumber AND b.sceneNumber < :sceneNumber) " +
           "OR c.chapterNumber < :chapterNumber) " +
           "ORDER BY c.chapterNumber DESC, b.sceneNumber DESC")
    Optional<Background> findPreviousBackground(@Param("novelId") Long novelId,
                                              @Param("chapterNumber") Integer chapterNumber,
                                              @Param("sceneNumber") Integer sceneNumber);
    
    /**
     * Get total scenes in a chapter by chapter ID
     */
    Long countByChapterId(Long chapterId);

    /**
     * Get total scenes in a chapter by novel ID and chapter number
     */
    @Query("SELECT COUNT(b) FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId AND c.chapterNumber = :chapterNumber")
    Long countScenesInChapter(@Param("novelId") Long novelId, @Param("chapterNumber") Integer chapterNumber);
    
    /**
     * Get max scene number for a chapter
     */
    @Query("SELECT MAX(b.sceneNumber) FROM Background b WHERE b.chapter.id = :chapterId")
    Optional<Integer> findMaxSceneNumberByChapterId(@Param("chapterId") Long chapterId);

    /**
     * Count total backgrounds/scenes for a novel
     */
    @Query("SELECT COUNT(b) FROM Background b JOIN b.chapter c WHERE c.novel.id = :novelId")
    Long countByNovelId(@Param("novelId") Long novelId);
}
