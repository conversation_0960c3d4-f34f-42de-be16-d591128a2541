package xyz.sky.pptgame.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import xyz.sky.pptgame.entity.Chapter;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChapterRepository extends JpaRepository<Chapter, Long> {
    
    /**
     * Find chapters by novel ID ordered by chapter number
     */
    List<Chapter> findByNovelIdOrderByChapterNumberAsc(Long novelId);
    
    /**
     * Find chapter by novel ID and chapter number
     */
    Optional<Chapter> findByNovelIdAndChapterNumber(Long novelId, Integer chapterNumber);
    
    /**
     * Find chapter with backgrounds
     */
    @Query("SELECT c FROM Chapter c LEFT JOIN FETCH c.backgrounds WHERE c.id = :id")
    Optional<Chapter> findByIdWithBackgrounds(@Param("id") Long id);
    
    /**
     * Find chapters with backgrounds by novel ID
     */
    @Query("SELECT c FROM Chapter c LEFT JOIN FETCH c.backgrounds b WHERE c.novel.id = :novelId ORDER BY c.chapterNumber ASC, b.sceneNumber ASC")
    List<Chapter> findByNovelIdWithBackgrounds(@Param("novelId") Long novelId);
    
    /**
     * Find completed chapters by novel ID
     */
    List<Chapter> findByNovelIdAndIsCompletedTrueOrderByChapterNumberAsc(Long novelId);
    
    /**
     * Find unlocked chapters by novel ID
     */
    List<Chapter> findByNovelIdAndIsUnlockedTrueOrderByChapterNumberAsc(Long novelId);
    
    /**
     * Find next chapter in sequence
     */
    @Query("SELECT c FROM Chapter c WHERE c.novel.id = :novelId AND c.chapterNumber > :currentChapterNumber ORDER BY c.chapterNumber ASC")
    Optional<Chapter> findNextChapter(@Param("novelId") Long novelId, @Param("currentChapterNumber") Integer currentChapterNumber);
    
    /**
     * Find previous chapter in sequence
     */
    @Query("SELECT c FROM Chapter c WHERE c.novel.id = :novelId AND c.chapterNumber < :currentChapterNumber ORDER BY c.chapterNumber DESC")
    Optional<Chapter> findPreviousChapter(@Param("novelId") Long novelId, @Param("currentChapterNumber") Integer currentChapterNumber);
    
    /**
     * Count chapters by novel ID
     */
    Long countByNovelId(Long novelId);
    
    /**
     * Count completed chapters by novel ID
     */
    Long countByNovelIdAndIsCompletedTrue(Long novelId);
    
    /**
     * Count unlocked chapters by novel ID
     */
    Long countByNovelIdAndIsUnlockedTrue(Long novelId);
    
    /**
     * Find chapters by difficulty level
     */
    List<Chapter> findByNovelIdAndDifficultyLevelOrderByChapterNumberAsc(Long novelId, Chapter.DifficultyLevel difficultyLevel);
    
    /**
     * Get max chapter number for a novel
     */
    @Query("SELECT MAX(c.chapterNumber) FROM Chapter c WHERE c.novel.id = :novelId")
    Optional<Integer> findMaxChapterNumberByNovelId(@Param("novelId") Long novelId);
    
    /**
     * Check if chapter exists by novel ID and chapter number
     */
    boolean existsByNovelIdAndChapterNumber(Long novelId, Integer chapterNumber);
    
    /**
     * Find chapters by title containing (case-insensitive)
     */
    List<Chapter> findByNovelIdAndTitleContainingIgnoreCaseOrderByChapterNumberAsc(Long novelId, String title);
}
