package xyz.sky.pptgame.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "dialogues")
@Data
@EqualsAndHashCode(exclude = {"character", "background"})
@ToString(exclude = {"character", "background"})
public class Dialogue {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Dialogue text cannot be blank")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String text;
    
    @NotNull(message = "Sequence order cannot be null")
    @Column(name = "sequence_order", nullable = false)
    private Integer sequenceOrder;
    
    @Column(name = "dialogue_type")
    @Enumerated(EnumType.STRING)
    private DialogueType dialogueType = DialogueType.SPEECH;
    
    @Column(name = "emotion")
    @Enumerated(EnumType.STRING)
    private Emotion emotion = Emotion.NEUTRAL;
    
    @Column(name = "voice_file_url")
    private String voiceFileUrl;
    
    @Column(name = "display_duration_ms")
    private Integer displayDurationMs;
    
    @Column(name = "auto_advance", nullable = false)
    private Boolean autoAdvance = false;
    
    @Column(name = "character_position")
    @Enumerated(EnumType.STRING)
    private CharacterPosition characterPosition = CharacterPosition.CENTER;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "character_id", nullable = false)
    private Character character;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "background_id", nullable = false)
    private Background background;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum DialogueType {
        SPEECH,
        THOUGHT,
        NARRATION,
        SYSTEM_MESSAGE
    }
    
    public enum Emotion {
        HAPPY,
        SAD,
        ANGRY,
        SURPRISED,
        CONFUSED,
        EXCITED,
        WORRIED,
        NEUTRAL
    }
    
    public enum CharacterPosition {
        LEFT,
        CENTER,
        RIGHT,
        OFF_SCREEN
    }
}
