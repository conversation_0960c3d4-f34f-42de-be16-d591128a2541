package xyz.sky.pptgame.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_actions")
@Data
@EqualsAndHashCode(exclude = {"background"})
@ToString(exclude = {"background"})
public class GameAction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "Action type cannot be null")
    @Column(name = "action_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ActionType actionType;
    
    @NotNull(message = "Sequence order cannot be null")
    @Column(name = "sequence_order", nullable = false)
    private Integer sequenceOrder;
    
    @Column(name = "target_element")
    private String targetElement;
    
    @Column(name = "action_value", columnDefinition = "TEXT")
    private String actionValue;
    
    @Column(name = "duration_ms")
    private Integer durationMs;
    
    @Column(name = "delay_before_ms")
    private Integer delayBeforeMs = 0;
    
    @Column(name = "trigger_condition")
    @Enumerated(EnumType.STRING)
    private TriggerCondition triggerCondition = TriggerCondition.CLICK;
    
    @Column(name = "is_blocking", nullable = false)
    private Boolean isBlocking = false;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "background_id", nullable = false)
    private Background background;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum ActionType {
        PLAY_MUSIC,
        STOP_MUSIC,
        CHANGE_BACKGROUND_IMAGE,
        SHOW_CHARACTER,
        HIDE_CHARACTER,
        MOVE_CHARACTER,
        PLAY_SOUND_EFFECT,
        SHOW_TEXT_OVERLAY,
        HIDE_TEXT_OVERLAY,
        FADE_IN,
        FADE_OUT,
        SCREEN_SHAKE,
        WEATHER_CHANGE,
        TIME_CHANGE,
        CUSTOM_ANIMATION
    }
    
    public enum TriggerCondition {
        CLICK,
        AUTO_TIMER,
        DIALOGUE_END,
        SCENE_START,
        CUSTOM_EVENT
    }
}
