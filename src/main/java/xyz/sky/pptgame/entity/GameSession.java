package xyz.sky.pptgame.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_sessions")
@Data
@EqualsAndHashCode(exclude = {"novel", "currentBackground"})
@ToString(exclude = {"novel", "currentBackground"})
public class GameSession {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Session ID cannot be blank")
    @Column(name = "session_id", nullable = false, unique = true)
    private String sessionId;
    
    @Column(name = "player_name")
    private String playerName;
    
    @NotNull(message = "Current chapter cannot be null")
    @Column(name = "current_chapter", nullable = false)
    private Integer currentChapter = 1;
    
    @NotNull(message = "Current scene cannot be null")
    @Column(name = "current_scene", nullable = false)
    private Integer currentScene = 1;
    
    @Column(name = "current_dialogue_index")
    private Integer currentDialogueIndex = 0;
    
    @Column(name = "current_action_index")
    private Integer currentActionIndex = 0;
    
    @Column(name = "session_state")
    @Enumerated(EnumType.STRING)
    private SessionState sessionState = SessionState.ACTIVE;
    
    @Column(name = "last_save_data", columnDefinition = "TEXT")
    private String lastSaveData;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "last_accessed_at")
    private LocalDateTime lastAccessedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "novel_id", nullable = false)
    private Novel novel;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "current_background_id")
    private Background currentBackground;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        lastAccessedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        lastAccessedAt = LocalDateTime.now();
    }
    
    public enum SessionState {
        ACTIVE,
        PAUSED,
        COMPLETED,
        ABANDONED
    }
}
