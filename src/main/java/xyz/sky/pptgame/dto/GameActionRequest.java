package xyz.sky.pptgame.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameActionRequest {
    
    private String sessionId;
    private ActionType actionType;
    private String targetElement;
    private Object actionData;
    
    public enum ActionType {
        NEXT_DIALOGUE,
        PREVIOUS_DIALOGUE,
        NEXT_SCENE,
        PREVIOUS_SCENE,
        JUMP_TO_CHAPTER,
        JUMP_TO_SCENE,
        EXECUTE_ACTION,
        SAVE_GAME,
        LOAD_GAME,
        PAUSE_GAME,
        RESUME_GAME
    }
}
