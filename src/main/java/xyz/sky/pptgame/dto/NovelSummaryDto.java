package xyz.sky.pptgame.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NovelSummaryDto {
    
    private Long id;
    private String title;
    private String description;
    private String author;
    private String coverImageUrl;
    private Integer totalChapters;
    private Boolean isCompleted;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long totalScenes;
    private Long activeSessions;
}
