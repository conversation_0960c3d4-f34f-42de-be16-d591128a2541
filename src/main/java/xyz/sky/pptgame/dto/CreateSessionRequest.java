package xyz.sky.pptgame.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSessionRequest {
    
    @NotNull(message = "Novel ID cannot be null")
    private Long novelId;
    
    private String playerName;
    
    private Integer startChapter = 1;
    
    private Integer startScene = 1;
    
    private String existingSessionId; // For resuming a session
}
