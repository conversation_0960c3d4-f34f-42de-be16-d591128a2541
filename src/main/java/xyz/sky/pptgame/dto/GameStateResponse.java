package xyz.sky.pptgame.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import xyz.sky.pptgame.entity.Background;
import xyz.sky.pptgame.entity.Dialogue;
import xyz.sky.pptgame.entity.GameAction;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameStateResponse {
    
    private String sessionId;
    private Long novelId;
    private String novelTitle;
    private Integer currentChapter;
    private Integer currentScene;
    private Integer currentDialogueIndex;
    private Integer currentActionIndex;
    private BackgroundDto currentBackground;
    private List<DialogueDto> dialogues;
    private List<GameActionDto> gameActions;
    private GameProgressDto progress;
    private boolean hasNext;
    private boolean hasPrevious;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BackgroundDto {
        private Long id;
        private String name;
        private String description;
        private Integer chapterNumber;
        private Integer sceneNumber;
        private String backgroundImageUrl;
        private String backgroundMusicUrl;
        private String ambientSoundUrl;
        private Background.WeatherEffect weatherEffect;
        private Background.TimeOfDay timeOfDay;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DialogueDto {
        private Long id;
        private String text;
        private Integer sequenceOrder;
        private Dialogue.DialogueType dialogueType;
        private Dialogue.Emotion emotion;
        private String voiceFileUrl;
        private Integer displayDurationMs;
        private Boolean autoAdvance;
        private Dialogue.CharacterPosition characterPosition;
        private CharacterDto character;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CharacterDto {
        private Long id;
        private String name;
        private String description;
        private String avatarUrl;
        private String characterType;
        private String voiceUrl;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameActionDto {
        private Long id;
        private GameAction.ActionType actionType;
        private Integer sequenceOrder;
        private String targetElement;
        private String actionValue;
        private Integer durationMs;
        private Integer delayBeforeMs;
        private GameAction.TriggerCondition triggerCondition;
        private Boolean isBlocking;
        private String description;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameProgressDto {
        private Integer totalChapters;
        private Integer totalScenesInChapter;
        private Integer totalDialoguesInScene;
        private Integer totalActionsInScene;
        private Double chapterProgress;
        private Double overallProgress;
    }
}
