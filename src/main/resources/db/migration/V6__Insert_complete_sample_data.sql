-- Insert sample novels
INSERT INTO novels (title, description, author, total_chapters, is_completed) VALUES
('The Mysterious Academy', 'A thrilling story about a student who discovers magical secrets in their academy.', '<PERSON>', 5, false),
('Love in the Digital Age', 'A romantic story about two programmers who fall in love through code.', '<PERSON>', 3, true),
('The Last Guardian', 'An epic fantasy adventure about the last guardian protecting the realm.', '<PERSON> Johnson', 8, false);

-- Insert sample characters
INSERT INTO characters (name, description, character_type, personality_traits) VALUES
('<PERSON>', 'The main protagonist, a curious and brave student.', 'PROTAGONIST', 'Curious, brave, intelligent'),
('Professor <PERSON>', 'A wise and mysterious teacher with hidden knowledge.', 'N<PERSON>', 'Wise, mysterious, knowledgeable'),
('<PERSON>', '<PERSON>''s best friend and study partner.', 'N<PERSON>', 'Loyal, supportive, cheerful'),
('The Narrator', 'The storyteller who guides the reader.', 'NARRATOR', 'Omniscient, neutral'),
('<PERSON>', 'A talented programmer working at a tech startup.', 'PROTAGONIST', 'Smart, ambitious, creative'),
('<PERSON>', 'A senior developer who becomes <PERSON>''s mentor and love interest.', '<PERSON><PERSON>', 'Experienced, patient, caring'),
('<PERSON>el', 'The last guardian with ancient powers.', 'PROTAGONIST', 'Noble, determined, powerful'),
('Elder Thorne', 'An ancient sage who guides Kael.', 'NPC', 'Ancient, wise, cryptic');

-- Insert chapters for "The Mysterious Academy"
INSERT INTO chapters (title, description, chapter_number, novel_id, is_unlocked, unlock_condition, estimated_duration_minutes, difficulty_level) VALUES
('The Arrival', 'Alex arrives at the mysterious academy and begins to discover its secrets.', 1, 1, TRUE, NULL, 30, 'EASY'),
('Hidden Depths', 'Alex delves deeper into the academy''s mysteries and uncovers ancient secrets.', 2, 1, FALSE, 'Complete Chapter 1', 45, 'NORMAL'),
('The Forbidden Library', 'Alex discovers a hidden section of the library with dangerous knowledge.', 3, 1, FALSE, 'Complete Chapter 2', 50, 'NORMAL'),
('The Ancient Ritual', 'Alex must participate in an ancient ritual to unlock their true potential.', 4, 1, FALSE, 'Complete Chapter 3', 60, 'HARD'),
('The Final Test', 'Alex faces the ultimate challenge to become a true guardian of the academy.', 5, 1, FALSE, 'Complete Chapter 4', 75, 'EXPERT');

-- Insert chapters for "Love in the Digital Age"
INSERT INTO chapters (title, description, chapter_number, novel_id, is_unlocked, unlock_condition, estimated_duration_minutes, difficulty_level) VALUES
('First Encounter', 'Maya and David meet and begin their professional relationship.', 1, 2, TRUE, NULL, 25, 'EASY'),
('Growing Closer', 'Maya and David work together on projects and their relationship deepens.', 2, 2, FALSE, 'Complete Chapter 1', 35, 'NORMAL'),
('Love Realized', 'Maya and David finally confess their feelings and find happiness together.', 3, 2, FALSE, 'Complete Chapter 2', 40, 'NORMAL');

-- Insert chapters for "The Last Guardian"
INSERT INTO chapters (title, description, chapter_number, novel_id, is_unlocked, unlock_condition, estimated_duration_minutes, difficulty_level) VALUES
('The Awakening', 'Kael discovers his destiny as the last guardian of the realm.', 1, 3, TRUE, NULL, 35, 'EASY'),
('The Ancient Prophecy', 'Kael learns about the ancient prophecy that foretells his role.', 2, 3, FALSE, 'Complete Chapter 1', 45, 'NORMAL'),
('The First Trial', 'Kael faces his first trial to prove his worthiness as a guardian.', 3, 3, FALSE, 'Complete Chapter 2', 55, 'NORMAL'),
('The Dark Forces', 'Kael encounters the dark forces threatening the realm.', 4, 3, FALSE, 'Complete Chapter 3', 65, 'HARD'),
('The Alliance', 'Kael forms alliances with other magical beings to fight the darkness.', 5, 3, FALSE, 'Complete Chapter 4', 70, 'HARD'),
('The Great Battle', 'Kael leads the final battle against the forces of darkness.', 6, 3, FALSE, 'Complete Chapter 5', 80, 'EXPERT'),
('The New Dawn', 'Kael establishes a new order and brings peace to the realm.', 7, 3, FALSE, 'Complete Chapter 6', 60, 'EXPERT'),
('Legacy', 'Kael reflects on his journey and prepares the next generation of guardians.', 8, 3, FALSE, 'Complete Chapter 7', 45, 'NORMAL');

-- Insert backgrounds for "The Mysterious Academy" - Chapter 1
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Academy Entrance', 'The grand entrance of the mysterious academy with towering spires.', 1, c.id, '/images/academy_entrance.jpg', 'MORNING'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 1;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Classroom 101', 'A traditional classroom with ancient books and mysterious symbols.', 2, c.id, '/images/classroom.jpg', 'MORNING'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 1;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Library', 'The vast academy library filled with ancient tomes and secrets.', 3, c.id, '/images/library.jpg', 'AFTERNOON'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 1;

-- Insert backgrounds for "The Mysterious Academy" - Chapter 2
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Secret Chamber', 'A hidden chamber beneath the academy discovered by Alex.', 1, c.id, '/images/secret_chamber.jpg', 'NIGHT'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 2;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Headmaster Office', 'The headmaster''s office with mysterious artifacts.', 2, c.id, '/images/headmaster_office.jpg', 'EVENING'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 2;

-- Insert backgrounds for "Love in the Digital Age" - Chapter 1
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Tech Office', 'A modern tech startup office with open workspaces.', 1, c.id, '/images/tech_office.jpg', 'MORNING'
FROM chapters c WHERE c.novel_id = 2 AND c.chapter_number = 1;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Coffee Shop', 'A cozy coffee shop where Maya and David first meet outside work.', 2, c.id, '/images/coffee_shop.jpg', 'AFTERNOON'
FROM chapters c WHERE c.novel_id = 2 AND c.chapter_number = 1;

-- Insert backgrounds for "The Last Guardian" - Chapter 1
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day)
SELECT 'Mountain Peak', 'A high mountain peak where Kael first awakens to his powers.', 1, c.id, '/images/mountain_peak.jpg', 'DAWN'
FROM chapters c WHERE c.novel_id = 3 AND c.chapter_number = 1;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day)
SELECT 'Ancient Temple', 'An ancient temple where Elder Thorne resides.', 2, c.id, '/images/ancient_temple.jpg', 'MORNING'
FROM chapters c WHERE c.novel_id = 3 AND c.chapter_number = 1;

-- Insert sample dialogues
-- Academy Chapter 1, Scene 1 (Academy Entrance)
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'Welcome to the Mysterious Academy, where knowledge and magic intertwine.', 0, 4, b.id, 'NARRATION', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 1;

INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'Wow, this place is incredible! I can''t believe I actually got accepted.', 1, 1, b.id, 'SPEECH', 'EXCITED'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 1;

INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'The academy has stood for over a thousand years, young one. Its secrets run deeper than you can imagine.', 2, 2, b.id, 'SPEECH', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 1;

-- Academy Chapter 1, Scene 2 (Classroom)
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'Today we begin your journey into the ancient arts. Pay close attention.', 0, 2, b.id, 'SPEECH', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 2;

INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'This is so different from regular school. Everything feels... magical.', 1, 1, b.id, 'THOUGHT', 'SURPRISED'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 2;

-- Tech Office dialogues
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'Another day, another bug to fix. At least the coffee is good here.', 0, 5, b.id, 'THOUGHT', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 2 AND c.chapter_number = 1 AND b.scene_number = 1;

INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'Maya, could you take a look at this code? I think there might be a more elegant solution.', 1, 6, b.id, 'SPEECH', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 2 AND c.chapter_number = 1 AND b.scene_number = 1;

-- Mountain Peak dialogues
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'The wind carries whispers of ancient power as Kael awakens.', 0, 4, b.id, 'NARRATION', 'NEUTRAL'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 3 AND c.chapter_number = 1 AND b.scene_number = 1;

INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion)
SELECT 'What... what is this power flowing through me?', 1, 7, b.id, 'SPEECH', 'SURPRISED'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 3 AND c.chapter_number = 1 AND b.scene_number = 1;

-- Insert sample game actions
-- Academy entrance actions
INSERT INTO game_actions (action_type, sequence_order, background_id, trigger_condition, description, action_value)
SELECT 'PLAY_MUSIC', 0, b.id, 'SCENE_START', 'Play academy entrance theme', '/audio/academy_theme.mp3'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 1;

INSERT INTO game_actions (action_type, sequence_order, background_id, trigger_condition, description, action_value)
SELECT 'FADE_IN', 1, b.id, 'SCENE_START', 'Fade in the academy entrance', '2000'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 1 AND c.chapter_number = 1 AND b.scene_number = 1;

-- Tech office actions
INSERT INTO game_actions (action_type, sequence_order, background_id, trigger_condition, description, action_value)
SELECT 'PLAY_MUSIC', 0, b.id, 'SCENE_START', 'Play office background music', '/audio/office_ambient.mp3'
FROM backgrounds b JOIN chapters c ON b.chapter_id = c.id
WHERE c.novel_id = 2 AND c.chapter_number = 1 AND b.scene_number = 1;
