-- Create chapters table
CREATE TABLE chapters (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    chapter_number INTEGER NOT NULL,
    cover_image_url VARCHAR(500),
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    is_unlocked BOOLEAN NOT NULL DEFAULT FALSE,
    unlock_condition TEXT,
    estimated_duration_minutes INTEGER,
    difficulty_level VARCHAR(50) NOT NULL DEFAULT 'NORMAL',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    novel_id BIGINT NOT NULL,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
);

-- Add indexes for chapters table
CREATE INDEX idx_chapters_novel_id ON chapters(novel_id);
CREATE INDEX idx_chapters_chapter_number ON chapters(novel_id, chapter_number);
CREATE INDEX idx_chapters_title ON chapters(title);
CREATE INDEX idx_chapters_is_completed ON chapters(is_completed);
CREATE INDEX idx_chapters_is_unlocked ON chapters(is_unlocked);
CREATE INDEX idx_chapters_difficulty ON chapters(difficulty_level);

-- Add unique constraint for chapter number within a novel
ALTER TABLE chapters ADD CONSTRAINT uk_chapters_novel_chapter 
    UNIQUE (novel_id, chapter_number);

-- Add check constraints for chapters
ALTER TABLE chapters ADD CONSTRAINT chk_chapters_chapter_number 
    CHECK (chapter_number > 0);

-- Add chapter_id column to backgrounds table
ALTER TABLE backgrounds ADD COLUMN chapter_id BIGINT;

-- Create a temporary function to migrate data
DO $$
DECLARE
    bg_record RECORD;
    chapter_id_val BIGINT;
BEGIN
    -- For each background, find or create the corresponding chapter
    FOR bg_record IN SELECT DISTINCT novel_id, chapter_number FROM backgrounds ORDER BY novel_id, chapter_number LOOP
        -- Check if chapter already exists
        SELECT id INTO chapter_id_val 
        FROM chapters 
        WHERE novel_id = bg_record.novel_id AND chapter_number = bg_record.chapter_number;
        
        -- If chapter doesn't exist, create it
        IF chapter_id_val IS NULL THEN
            INSERT INTO chapters (title, chapter_number, novel_id, is_unlocked)
            VALUES (
                'Chapter ' || bg_record.chapter_number,
                bg_record.chapter_number,
                bg_record.novel_id,
                CASE WHEN bg_record.chapter_number = 1 THEN TRUE ELSE FALSE END
            )
            RETURNING id INTO chapter_id_val;
        END IF;
        
        -- Update backgrounds to reference the chapter
        UPDATE backgrounds 
        SET chapter_id = chapter_id_val 
        WHERE novel_id = bg_record.novel_id AND chapter_number = bg_record.chapter_number;
    END LOOP;
END $$;

-- Make chapter_id NOT NULL after data migration
ALTER TABLE backgrounds ALTER COLUMN chapter_id SET NOT NULL;

-- Add foreign key constraint
ALTER TABLE backgrounds ADD CONSTRAINT fk_backgrounds_chapter 
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE;

-- Add index for chapter_id in backgrounds
CREATE INDEX idx_backgrounds_chapter_id ON backgrounds(chapter_id);

-- Drop the old novel_id foreign key constraint and column from backgrounds
ALTER TABLE backgrounds DROP CONSTRAINT backgrounds_novel_id_fkey;
ALTER TABLE backgrounds DROP COLUMN novel_id;

-- Drop the old chapter_number column from backgrounds since it's now in chapters
ALTER TABLE backgrounds DROP COLUMN chapter_number;

-- Update the unique constraint for backgrounds
ALTER TABLE backgrounds DROP CONSTRAINT uk_backgrounds_novel_chapter_scene;
ALTER TABLE backgrounds ADD CONSTRAINT uk_backgrounds_chapter_scene 
    UNIQUE (chapter_id, scene_number);

-- Add current_chapter_id column to game_sessions table
ALTER TABLE game_sessions ADD COLUMN current_chapter_id BIGINT;

-- Migrate existing game session data
DO $$
DECLARE
    session_record RECORD;
    chapter_id_val BIGINT;
BEGIN
    FOR session_record IN SELECT id, novel_id, current_chapter FROM game_sessions LOOP
        -- Find the chapter ID for the current chapter number
        SELECT id INTO chapter_id_val 
        FROM chapters 
        WHERE novel_id = session_record.novel_id AND chapter_number = session_record.current_chapter;
        
        -- Update the session with the chapter ID
        IF chapter_id_val IS NOT NULL THEN
            UPDATE game_sessions 
            SET current_chapter_id = chapter_id_val 
            WHERE id = session_record.id;
        END IF;
    END LOOP;
END $$;

-- Rename current_chapter to current_chapter_number for clarity
ALTER TABLE game_sessions RENAME COLUMN current_chapter TO current_chapter_number;

-- Add foreign key constraint for current_chapter_id
ALTER TABLE game_sessions ADD CONSTRAINT fk_game_sessions_current_chapter 
    FOREIGN KEY (current_chapter_id) REFERENCES chapters(id) ON DELETE SET NULL;

-- Add index for current_chapter_id
CREATE INDEX idx_game_sessions_current_chapter ON game_sessions(current_chapter_id);

-- Update check constraint for game_sessions
ALTER TABLE game_sessions DROP CONSTRAINT chk_game_sessions_current_chapter;
ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_current_chapter_number 
    CHECK (current_chapter_number > 0);
