-- Insert sample novels
INSERT INTO novels (title, description, author, total_chapters, is_completed) VALUES
('The Mysterious Academy', 'A thrilling story about a student who discovers magical secrets in their academy.', '<PERSON>', 5, false),
('Love in the Digital Age', 'A romantic story about two programmers who fall in love through code.', '<PERSON>', 3, true),
('The Last Guardian', 'An epic fantasy adventure about the last guardian protecting the realm.', '<PERSON> Johnson', 8, false);

-- Insert sample characters
INSERT INTO characters (name, description, character_type, personality_traits) VALUES
('<PERSON>', 'The main protagonist, a curious and brave student.', 'PROTAGONIST', 'Curious, brave, intelligent'),
('Professor <PERSON>', 'A wise and mysterious teacher with hidden knowledge.', 'N<PERSON>', 'Wise, mysterious, knowledgeable'),
('<PERSON>', '<PERSON>''s best friend and study partner.', 'N<PERSON>', 'Loyal, supportive, cheerful'),
('The Narrator', 'The storyteller who guides the reader.', 'NARRATOR', 'Omniscient, neutral'),
('<PERSON>', 'A talented programmer working at a tech startup.', 'PROTAGONIST', 'Smart, ambitious, creative'),
('<PERSON>', 'A senior developer who becomes <PERSON>''s mentor and love interest.', '<PERSON><PERSON>', 'Experienced, patient, caring'),
('<PERSON>el', 'The last guardian with ancient powers.', 'PROTAGONIST', 'Noble, determined, powerful'),
('Elder Thorne', 'An ancient sage who guides Kael.', 'NPC', 'Ancient, wise, cryptic');

-- Insert sample backgrounds for "The Mysterious Academy"
INSERT INTO backgrounds (name, description, chapter_number, scene_number, novel_id, background_image_url, time_of_day) VALUES
('Academy Entrance', 'The grand entrance of the mysterious academy with towering spires.', 1, 1, 1, '/images/academy_entrance.jpg', 'MORNING'),
('Classroom 101', 'A traditional classroom with ancient books and mysterious symbols.', 1, 2, 1, '/images/classroom.jpg', 'MORNING'),
('Library', 'The vast academy library filled with ancient tomes and secrets.', 1, 3, 1, '/images/library.jpg', 'AFTERNOON'),
('Secret Chamber', 'A hidden chamber beneath the academy discovered by Alex.', 2, 1, 1, '/images/secret_chamber.jpg', 'NIGHT'),
('Headmaster Office', 'The headmaster''s office with mysterious artifacts.', 2, 2, 1, '/images/headmaster_office.jpg', 'EVENING');

-- Insert sample backgrounds for "Love in the Digital Age"
INSERT INTO backgrounds (name, description, chapter_number, scene_number, novel_id, background_image_url, time_of_day) VALUES
('Tech Office', 'A modern tech startup office with open workspaces.', 1, 1, 2, '/images/tech_office.jpg', 'MORNING'),
('Coffee Shop', 'A cozy coffee shop where Maya and David first meet outside work.', 1, 2, 2, '/images/coffee_shop.jpg', 'AFTERNOON'),
('Maya Apartment', 'Maya''s modern apartment with coding setup.', 2, 1, 2, '/images/apartment.jpg', 'EVENING'),
('Conference Room', 'The company conference room during an important presentation.', 3, 1, 2, '/images/conference_room.jpg', 'MORNING');

-- Insert sample dialogues for Academy Chapter 1, Scene 1
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
('Welcome to the Mysterious Academy, where knowledge and magic intertwine.', 0, 4, 1, 'NARRATION', 'NEUTRAL'),
('Wow, this place is incredible! I can''t believe I actually got accepted.', 1, 1, 1, 'SPEECH', 'EXCITED'),
('The academy has stood for over a thousand years, young one. Its secrets run deeper than you can imagine.', 2, 2, 1, 'SPEECH', 'NEUTRAL'),
('Secrets? What kind of secrets?', 3, 1, 1, 'SPEECH', 'CURIOUS');

-- Insert sample dialogues for Academy Chapter 1, Scene 2
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
('Today we begin your journey into the ancient arts. Pay close attention.', 0, 2, 2, 'SPEECH', 'NEUTRAL'),
('This is so different from regular school. Everything feels... magical.', 1, 1, 2, 'THOUGHT', 'SURPRISED'),
('Alex, would you please read from page 47 of the Codex Mysterium?', 2, 2, 2, 'SPEECH', 'NEUTRAL'),
('The words... they''re glowing! Professor, is this normal?', 3, 1, 2, 'SPEECH', 'SURPRISED');

-- Insert sample dialogues for Tech Office scene
INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
('Another day, another bug to fix. At least the coffee is good here.', 0, 5, 6, 'THOUGHT', 'NEUTRAL'),
('Maya, could you take a look at this code? I think there might be a more elegant solution.', 1, 6, 6, 'SPEECH', 'NEUTRAL'),
('Sure! I love a good coding challenge.', 2, 5, 6, 'SPEECH', 'EXCITED'),
('Your approach is brilliant. I never thought of using recursion there.', 3, 6, 6, 'SPEECH', 'HAPPY');

-- Insert sample game actions
INSERT INTO game_actions (action_type, sequence_order, background_id, trigger_condition, description, action_value) VALUES
('PLAY_MUSIC', 0, 1, 'SCENE_START', 'Play academy entrance theme', '/audio/academy_theme.mp3'),
('FADE_IN', 1, 1, 'SCENE_START', 'Fade in the academy entrance', '2000'),
('SHOW_CHARACTER', 2, 1, 'DIALOGUE_END', 'Show Alex character', 'alex_normal'),
('PLAY_SOUND_EFFECT', 0, 4, 'SCENE_START', 'Play mysterious chamber ambience', '/audio/chamber_ambience.mp3'),
('SCREEN_SHAKE', 1, 4, 'CUSTOM_EVENT', 'Shake screen when magic activates', '500'),
('PLAY_MUSIC', 0, 6, 'SCENE_START', 'Play office background music', '/audio/office_ambient.mp3'),
('SHOW_CHARACTER', 1, 6, 'DIALOGUE_END', 'Show Maya at her desk', 'maya_working');

-- Update sequences to ensure proper ordering
UPDATE dialogues SET sequence_order = sequence_order WHERE background_id IN (1, 2, 6);
UPDATE game_actions SET sequence_order = sequence_order WHERE background_id IN (1, 4, 6);
