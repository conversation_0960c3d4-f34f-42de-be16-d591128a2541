-- Update existing chapters with better titles and descriptions
UPDATE chapters SET 
    title = 'The Arrival',
    description = '<PERSON> arrives at the mysterious academy and begins to discover its secrets.',
    is_unlocked = TRUE,
    estimated_duration_minutes = 30,
    difficulty_level = 'EASY'
WHERE novel_id = 1 AND chapter_number = 1;

UPDATE chapters SET 
    title = 'Hidden Depths',
    description = '<PERSON> delves deeper into the academy''s mysteries and uncovers ancient secrets.',
    unlock_condition = 'Complete Chapter 1',
    estimated_duration_minutes = 45,
    difficulty_level = 'NORMAL'
WHERE novel_id = 1 AND chapter_number = 2;

-- Add more chapters for "The Mysterious Academy"
INSERT INTO chapters (title, description, chapter_number, novel_id, is_unlocked, unlock_condition, estimated_duration_minutes, difficulty_level) VALUES
('The Forbidden Library', '<PERSON> discovers a hidden section of the library with dangerous knowledge.', 3, 1, FALSE, 'Complete Chapter 2', 50, 'NORMAL'),
('The Ancient Ritual', '<PERSON> must participate in an ancient ritual to unlock their true potential.', 4, 1, FALSE, 'Complete Chapter 3', 60, 'HARD'),
('The Final Test', '<PERSON> faces the ultimate challenge to become a true guardian of the academy.', 5, 1, FALSE, 'Complete Chapter 4', 75, 'EXPERT');

-- Update chapters for "Love in the Digital Age"
UPDATE chapters SET 
    title = 'First Encounter',
    description = 'Maya and David meet and begin their professional relationship.',
    is_unlocked = TRUE,
    estimated_duration_minutes = 25,
    difficulty_level = 'EASY'
WHERE novel_id = 2 AND chapter_number = 1;

UPDATE chapters SET 
    title = 'Growing Closer',
    description = 'Maya and <PERSON> work together on projects and their relationship deepens.',
    unlock_condition = 'Complete Chapter 1',
    estimated_duration_minutes = 35,
    difficulty_level = 'NORMAL'
WHERE novel_id = 2 AND chapter_number = 2;

UPDATE chapters SET 
    title = 'Love Realized',
    description = 'Maya and David finally confess their feelings and find happiness together.',
    unlock_condition = 'Complete Chapter 2',
    estimated_duration_minutes = 40,
    difficulty_level = 'NORMAL'
WHERE novel_id = 2 AND chapter_number = 3;

-- Add chapters for "The Last Guardian"
INSERT INTO chapters (title, description, chapter_number, novel_id, is_unlocked, unlock_condition, estimated_duration_minutes, difficulty_level) VALUES
('The Awakening', 'Kael discovers his destiny as the last guardian of the realm.', 1, 3, TRUE, NULL, 35, 'EASY'),
('The Ancient Prophecy', 'Kael learns about the ancient prophecy that foretells his role.', 2, 3, FALSE, 'Complete Chapter 1', 45, 'NORMAL'),
('The First Trial', 'Kael faces his first trial to prove his worthiness as a guardian.', 3, 3, FALSE, 'Complete Chapter 2', 55, 'NORMAL'),
('The Dark Forces', 'Kael encounters the dark forces threatening the realm.', 4, 3, FALSE, 'Complete Chapter 3', 65, 'HARD'),
('The Alliance', 'Kael forms alliances with other magical beings to fight the darkness.', 5, 3, FALSE, 'Complete Chapter 4', 70, 'HARD'),
('The Great Battle', 'Kael leads the final battle against the forces of darkness.', 6, 3, FALSE, 'Complete Chapter 5', 80, 'EXPERT'),
('The New Dawn', 'Kael establishes a new order and brings peace to the realm.', 7, 3, FALSE, 'Complete Chapter 6', 60, 'EXPERT'),
('Legacy', 'Kael reflects on his journey and prepares the next generation of guardians.', 8, 3, FALSE, 'Complete Chapter 7', 45, 'NORMAL');

-- Add more backgrounds for the new chapters
-- Chapter 3 of "The Mysterious Academy"
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Forbidden Section', 'A dark and mysterious section of the library filled with ancient tomes.', 1, c.id, '/images/forbidden_library.jpg', 'NIGHT'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 3;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Ancient Vault', 'A hidden vault beneath the library containing powerful artifacts.', 2, c.id, '/images/ancient_vault.jpg', 'NIGHT'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 3;

-- Chapter 4 of "The Mysterious Academy"
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Ritual Chamber', 'A sacred chamber where ancient rituals are performed.', 1, c.id, '/images/ritual_chamber.jpg', 'MIDNIGHT'
FROM chapters c WHERE c.novel_id = 1 AND c.chapter_number = 4;

-- Chapter 1 of "The Last Guardian"
INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Mountain Peak', 'A high mountain peak where Kael first awakens to his powers.', 1, c.id, '/images/mountain_peak.jpg', 'DAWN'
FROM chapters c WHERE c.novel_id = 3 AND c.chapter_number = 1;

INSERT INTO backgrounds (name, description, scene_number, chapter_id, background_image_url, time_of_day) 
SELECT 'Ancient Temple', 'An ancient temple where Elder Thorne resides.', 2, c.id, '/images/ancient_temple.jpg', 'MORNING'
FROM chapters c WHERE c.novel_id = 3 AND c.chapter_number = 1;

-- Add some dialogues for the new backgrounds
-- Get the background IDs for the new scenes
DO $$
DECLARE
    forbidden_bg_id BIGINT;
    ritual_bg_id BIGINT;
    mountain_bg_id BIGINT;
    temple_bg_id BIGINT;
BEGIN
    -- Get background IDs
    SELECT b.id INTO forbidden_bg_id 
    FROM backgrounds b 
    JOIN chapters c ON b.chapter_id = c.id 
    WHERE c.novel_id = 1 AND c.chapter_number = 3 AND b.scene_number = 1;
    
    SELECT b.id INTO ritual_bg_id 
    FROM backgrounds b 
    JOIN chapters c ON b.chapter_id = c.id 
    WHERE c.novel_id = 1 AND c.chapter_number = 4 AND b.scene_number = 1;
    
    SELECT b.id INTO mountain_bg_id 
    FROM backgrounds b 
    JOIN chapters c ON b.chapter_id = c.id 
    WHERE c.novel_id = 3 AND c.chapter_number = 1 AND b.scene_number = 1;
    
    SELECT b.id INTO temple_bg_id 
    FROM backgrounds b 
    JOIN chapters c ON b.chapter_id = c.id 
    WHERE c.novel_id = 3 AND c.chapter_number = 1 AND b.scene_number = 2;
    
    -- Add dialogues for forbidden library scene
    IF forbidden_bg_id IS NOT NULL THEN
        INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
        ('This section of the library feels different... darker somehow.', 0, 1, forbidden_bg_id, 'THOUGHT', 'WORRIED'),
        ('You should not be here, young one. These books contain knowledge too dangerous for novices.', 1, 2, forbidden_bg_id, 'SPEECH', 'WORRIED'),
        ('But Professor, I need to understand what''s happening to me. The magic... it''s getting stronger.', 2, 1, forbidden_bg_id, 'SPEECH', 'CONFUSED');
    END IF;
    
    -- Add dialogues for ritual chamber scene
    IF ritual_bg_id IS NOT NULL THEN
        INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
        ('The ancient ritual chamber pulses with mystical energy.', 0, 4, ritual_bg_id, 'NARRATION', 'NEUTRAL'),
        ('Are you ready, Alex? This ritual will awaken your true potential, but it cannot be undone.', 1, 2, ritual_bg_id, 'SPEECH', 'NEUTRAL'),
        ('I''m ready. I need to know who I really am.', 2, 1, ritual_bg_id, 'SPEECH', 'EXCITED');
    END IF;
    
    -- Add dialogues for mountain peak scene
    IF mountain_bg_id IS NOT NULL THEN
        INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
        ('The wind carries whispers of ancient power as Kael awakens.', 0, 4, mountain_bg_id, 'NARRATION', 'NEUTRAL'),
        ('What... what is this power flowing through me?', 1, 7, mountain_bg_id, 'SPEECH', 'SURPRISED'),
        ('The time has come, young guardian. Your destiny awaits.', 2, 8, mountain_bg_id, 'SPEECH', 'NEUTRAL');
    END IF;
    
    -- Add dialogues for temple scene
    IF temple_bg_id IS NOT NULL THEN
        INSERT INTO dialogues (text, sequence_order, character_id, background_id, dialogue_type, emotion) VALUES
        ('Welcome to the Temple of Guardians, Kael. I have been expecting you.', 0, 8, temple_bg_id, 'SPEECH', 'NEUTRAL'),
        ('Elder Thorne? How do you know my name? How do you know I would come here?', 1, 7, temple_bg_id, 'SPEECH', 'CONFUSED'),
        ('The prophecy foretold your arrival. You are the last of the guardian bloodline.', 2, 8, temple_bg_id, 'SPEECH', 'NEUTRAL');
    END IF;
END $$;
