-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS game_actions CASCADE;
DROP TABLE IF EXISTS dialogues CASCADE;
DROP TABLE IF EXISTS backgrounds CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS game_sessions CASCADE;
DROP TABLE IF EXISTS characters CASCADE;
DROP TABLE IF EXISTS novels CASCADE;

-- Create novels table
CREATE TABLE novels (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    author VARCHA<PERSON>(255) NOT NULL,
    cover_image_url VARCHAR(500),
    total_chapters INTEGER NOT NULL,
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create characters table
CREATE TABLE characters (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    avatar_url VARCHAR(500),
    character_type VARCHAR(50) NOT NULL DEFAULT 'NPC',
    voice_url VARCHAR(500),
    personality_traits TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create chapters table
CREATE TABLE chapters (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    chapter_number INTEGER NOT NULL,
    cover_image_url VARCHAR(500),
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    is_unlocked BOOLEAN NOT NULL DEFAULT FALSE,
    unlock_condition TEXT,
    estimated_duration_minutes INTEGER,
    difficulty_level VARCHAR(50) NOT NULL DEFAULT 'NORMAL',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    novel_id BIGINT NOT NULL,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
);

-- Create backgrounds table
CREATE TABLE backgrounds (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    scene_number INTEGER NOT NULL,
    background_image_url VARCHAR(500),
    background_music_url VARCHAR(500),
    ambient_sound_url VARCHAR(500),
    weather_effect VARCHAR(50),
    time_of_day VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    chapter_id BIGINT NOT NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE
);

-- Create dialogues table
CREATE TABLE dialogues (
    id BIGSERIAL PRIMARY KEY,
    text TEXT NOT NULL,
    sequence_order INTEGER NOT NULL,
    dialogue_type VARCHAR(50) NOT NULL DEFAULT 'SPEECH',
    emotion VARCHAR(50) NOT NULL DEFAULT 'NEUTRAL',
    voice_file_url VARCHAR(500),
    display_duration_ms INTEGER,
    auto_advance BOOLEAN NOT NULL DEFAULT FALSE,
    character_position VARCHAR(50) NOT NULL DEFAULT 'CENTER',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    character_id BIGINT NOT NULL,
    background_id BIGINT NOT NULL,
    FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
    FOREIGN KEY (background_id) REFERENCES backgrounds(id) ON DELETE CASCADE
);

-- Create game_actions table
CREATE TABLE game_actions (
    id BIGSERIAL PRIMARY KEY,
    action_type VARCHAR(50) NOT NULL,
    sequence_order INTEGER NOT NULL,
    target_element VARCHAR(255),
    action_value TEXT,
    duration_ms INTEGER,
    delay_before_ms INTEGER DEFAULT 0,
    trigger_condition VARCHAR(50) NOT NULL DEFAULT 'CLICK',
    is_blocking BOOLEAN NOT NULL DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    background_id BIGINT NOT NULL,
    FOREIGN KEY (background_id) REFERENCES backgrounds(id) ON DELETE CASCADE
);

-- Create game_sessions table
CREATE TABLE game_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    player_name VARCHAR(255),
    current_chapter_number INTEGER NOT NULL DEFAULT 1,
    current_scene INTEGER NOT NULL DEFAULT 1,
    current_dialogue_index INTEGER DEFAULT 0,
    current_action_index INTEGER DEFAULT 0,
    session_state VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    last_save_data TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    novel_id BIGINT NOT NULL,
    current_chapter_id BIGINT,
    current_background_id BIGINT,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    FOREIGN KEY (current_chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (current_background_id) REFERENCES backgrounds(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_novels_author ON novels(author);
CREATE INDEX idx_novels_title ON novels(title);
CREATE INDEX idx_novels_is_completed ON novels(is_completed);
CREATE INDEX idx_novels_created_at ON novels(created_at);

CREATE INDEX idx_characters_name ON characters(name);
CREATE INDEX idx_characters_type ON characters(character_type);

CREATE INDEX idx_chapters_novel_id ON chapters(novel_id);
CREATE INDEX idx_chapters_chapter_number ON chapters(novel_id, chapter_number);
CREATE INDEX idx_chapters_title ON chapters(title);
CREATE INDEX idx_chapters_is_completed ON chapters(is_completed);
CREATE INDEX idx_chapters_is_unlocked ON chapters(is_unlocked);
CREATE INDEX idx_chapters_difficulty ON chapters(difficulty_level);

CREATE INDEX idx_backgrounds_chapter_id ON backgrounds(chapter_id);
CREATE INDEX idx_backgrounds_scene_number ON backgrounds(chapter_id, scene_number);

CREATE INDEX idx_dialogues_background_id ON dialogues(background_id);
CREATE INDEX idx_dialogues_character_id ON dialogues(character_id);
CREATE INDEX idx_dialogues_sequence ON dialogues(background_id, sequence_order);
CREATE INDEX idx_dialogues_type ON dialogues(dialogue_type);

CREATE INDEX idx_game_actions_background_id ON game_actions(background_id);
CREATE INDEX idx_game_actions_sequence ON game_actions(background_id, sequence_order);
CREATE INDEX idx_game_actions_type ON game_actions(action_type);
CREATE INDEX idx_game_actions_trigger ON game_actions(trigger_condition);

CREATE INDEX idx_game_sessions_session_id ON game_sessions(session_id);
CREATE INDEX idx_game_sessions_novel_id ON game_sessions(novel_id);
CREATE INDEX idx_game_sessions_state ON game_sessions(session_state);
CREATE INDEX idx_game_sessions_last_accessed ON game_sessions(last_accessed_at);
CREATE INDEX idx_game_sessions_current_chapter ON game_sessions(current_chapter_id);

-- Add unique constraints
ALTER TABLE chapters ADD CONSTRAINT uk_chapters_novel_chapter 
    UNIQUE (novel_id, chapter_number);

ALTER TABLE backgrounds ADD CONSTRAINT uk_backgrounds_chapter_scene 
    UNIQUE (chapter_id, scene_number);

ALTER TABLE dialogues ADD CONSTRAINT uk_dialogues_background_sequence 
    UNIQUE (background_id, sequence_order);

ALTER TABLE game_actions ADD CONSTRAINT uk_game_actions_background_sequence 
    UNIQUE (background_id, sequence_order);

-- Add check constraints
ALTER TABLE novels ADD CONSTRAINT chk_novels_total_chapters 
    CHECK (total_chapters > 0);

ALTER TABLE chapters ADD CONSTRAINT chk_chapters_chapter_number 
    CHECK (chapter_number > 0);

ALTER TABLE backgrounds ADD CONSTRAINT chk_backgrounds_scene_number 
    CHECK (scene_number > 0);

ALTER TABLE dialogues ADD CONSTRAINT chk_dialogues_sequence_order 
    CHECK (sequence_order >= 0);

ALTER TABLE game_actions ADD CONSTRAINT chk_game_actions_sequence_order 
    CHECK (sequence_order >= 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_current_chapter_number 
    CHECK (current_chapter_number > 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_current_scene 
    CHECK (current_scene > 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_dialogue_index 
    CHECK (current_dialogue_index >= 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_action_index 
    CHECK (current_action_index >= 0);
