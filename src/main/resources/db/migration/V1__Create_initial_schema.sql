-- Create novels table
CREATE TABLE novels (
    id BIGSERIAL PRIMARY KEY,
    title VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    author VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    cover_image_url VARCHAR(500),
    total_chapters INTEGER NOT NULL,
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create characters table
CREATE TABLE characters (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar_url VARCHAR(500),
    character_type VARCHAR(50) NOT NULL DEFAULT 'NPC',
    voice_url VARCHAR(500),
    personality_traits TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create backgrounds table
CREATE TABLE backgrounds (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    chapter_number INTEGER NOT NULL,
    scene_number INTEGER NOT NULL,
    background_image_url VARCHAR(500),
    background_music_url VARCHAR(500),
    ambient_sound_url VARCHAR(500),
    weather_effect VARCHAR(50),
    time_of_day VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    novel_id BIGINT NOT NULL,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
);

-- Create dialogues table
CREATE TABLE dialogues (
    id BIGSERIAL PRIMARY KEY,
    text TEXT NOT NULL,
    sequence_order INTEGER NOT NULL,
    dialogue_type VARCHAR(50) NOT NULL DEFAULT 'SPEECH',
    emotion VARCHAR(50) NOT NULL DEFAULT 'NEUTRAL',
    voice_file_url VARCHAR(500),
    display_duration_ms INTEGER,
    auto_advance BOOLEAN NOT NULL DEFAULT FALSE,
    character_position VARCHAR(50) NOT NULL DEFAULT 'CENTER',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    character_id BIGINT NOT NULL,
    background_id BIGINT NOT NULL,
    FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
    FOREIGN KEY (background_id) REFERENCES backgrounds(id) ON DELETE CASCADE
);

-- Create game_actions table
CREATE TABLE game_actions (
    id BIGSERIAL PRIMARY KEY,
    action_type VARCHAR(50) NOT NULL,
    sequence_order INTEGER NOT NULL,
    target_element VARCHAR(255),
    action_value TEXT,
    duration_ms INTEGER,
    delay_before_ms INTEGER DEFAULT 0,
    trigger_condition VARCHAR(50) NOT NULL DEFAULT 'CLICK',
    is_blocking BOOLEAN NOT NULL DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    background_id BIGINT NOT NULL,
    FOREIGN KEY (background_id) REFERENCES backgrounds(id) ON DELETE CASCADE
);

-- Create game_sessions table
CREATE TABLE game_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    player_name VARCHAR(255),
    current_chapter INTEGER NOT NULL DEFAULT 1,
    current_scene INTEGER NOT NULL DEFAULT 1,
    current_dialogue_index INTEGER DEFAULT 0,
    current_action_index INTEGER DEFAULT 0,
    session_state VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    last_save_data TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    novel_id BIGINT NOT NULL,
    current_background_id BIGINT,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    FOREIGN KEY (current_background_id) REFERENCES backgrounds(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_novels_author ON novels(author);
CREATE INDEX idx_novels_title ON novels(title);
CREATE INDEX idx_novels_is_completed ON novels(is_completed);
CREATE INDEX idx_novels_created_at ON novels(created_at);

CREATE INDEX idx_characters_name ON characters(name);
CREATE INDEX idx_characters_type ON characters(character_type);

CREATE INDEX idx_backgrounds_novel_id ON backgrounds(novel_id);
CREATE INDEX idx_backgrounds_chapter_scene ON backgrounds(novel_id, chapter_number, scene_number);
CREATE INDEX idx_backgrounds_chapter ON backgrounds(novel_id, chapter_number);

CREATE INDEX idx_dialogues_background_id ON dialogues(background_id);
CREATE INDEX idx_dialogues_character_id ON dialogues(character_id);
CREATE INDEX idx_dialogues_sequence ON dialogues(background_id, sequence_order);
CREATE INDEX idx_dialogues_type ON dialogues(dialogue_type);

CREATE INDEX idx_game_actions_background_id ON game_actions(background_id);
CREATE INDEX idx_game_actions_sequence ON game_actions(background_id, sequence_order);
CREATE INDEX idx_game_actions_type ON game_actions(action_type);
CREATE INDEX idx_game_actions_trigger ON game_actions(trigger_condition);

CREATE INDEX idx_game_sessions_session_id ON game_sessions(session_id);
CREATE INDEX idx_game_sessions_novel_id ON game_sessions(novel_id);
CREATE INDEX idx_game_sessions_state ON game_sessions(session_state);
CREATE INDEX idx_game_sessions_last_accessed ON game_sessions(last_accessed_at);

-- Add constraints for data integrity
ALTER TABLE backgrounds ADD CONSTRAINT uk_backgrounds_novel_chapter_scene 
    UNIQUE (novel_id, chapter_number, scene_number);

ALTER TABLE dialogues ADD CONSTRAINT uk_dialogues_background_sequence 
    UNIQUE (background_id, sequence_order);

ALTER TABLE game_actions ADD CONSTRAINT uk_game_actions_background_sequence 
    UNIQUE (background_id, sequence_order);

-- Add check constraints
ALTER TABLE novels ADD CONSTRAINT chk_novels_total_chapters 
    CHECK (total_chapters > 0);

ALTER TABLE backgrounds ADD CONSTRAINT chk_backgrounds_chapter_number 
    CHECK (chapter_number > 0);

ALTER TABLE backgrounds ADD CONSTRAINT chk_backgrounds_scene_number 
    CHECK (scene_number > 0);

ALTER TABLE dialogues ADD CONSTRAINT chk_dialogues_sequence_order 
    CHECK (sequence_order >= 0);

ALTER TABLE game_actions ADD CONSTRAINT chk_game_actions_sequence_order 
    CHECK (sequence_order >= 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_current_chapter 
    CHECK (current_chapter > 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_current_scene 
    CHECK (current_scene > 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_dialogue_index 
    CHECK (current_dialogue_index >= 0);

ALTER TABLE game_sessions ADD CONSTRAINT chk_game_sessions_action_index 
    CHECK (current_action_index >= 0);
