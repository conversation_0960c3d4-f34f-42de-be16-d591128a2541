spring.application.name=ppt-game
server.port=9999
# Database Configuration
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*************************************
spring.datasource.username=game
spring.datasource.password=game

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.locations=classpath:db/migration

# Logging Configuration
logging.level.xyz.sky.pptgame=INFO
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# API Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html

