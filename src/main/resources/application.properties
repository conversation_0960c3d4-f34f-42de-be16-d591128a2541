spring.application.name=ppt-game
spring.jpa.hibernate.ddl-auto=validate
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*************************************
spring.datasource.username=game
spring.datasource.password=game
# some prisma schemes aren't following proper naming schemes, so this forces springboot to use the name as it appears in the column annotation
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl